<?php
/**
 * Network Management API for Enhanced Smart Contracts
 * API إدارة الشبكات للعقود الذكية المحسنة
 * 
 * يوفر وظائف إدارة الشبكات (إضافة، تعديل، حذف، تحقق)
 */

// إعدادات CORS
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// تضمين الملفات المطلوبة
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../middleware/admin_auth.php';

/**
 * فئة إدارة الشبكات
 */
class NetworkManagementAPI {
    private $db;
    
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * معالجة الطلبات
     */
    public function handleRequest() {
        // التحقق من صلاحيات الإدارة
        $adminAuth = checkAdminAuth();
        if (!$adminAuth['success']) {
            $this->sendError('Unauthorized access', 401);
            return;
        }
        
        $method = $_SERVER['REQUEST_METHOD'];
        $action = $_GET['action'] ?? '';
        
        try {
            switch ($method) {
                case 'GET':
                    $this->handleGet($action);
                    break;
                case 'POST':
                    $this->handlePost($action);
                    break;
                case 'PUT':
                    $this->handlePut($action);
                    break;
                case 'DELETE':
                    $this->handleDelete($action);
                    break;
                default:
                    $this->sendError('Method not allowed', 405);
            }
        } catch (Exception $e) {
            $this->sendError($e->getMessage(), 500);
        }
    }
    
    /**
     * معالجة طلبات GET
     */
    private function handleGet($action) {
        switch ($action) {
            case 'list':
                $this->getNetworksList();
                break;
            case 'details':
                $networkId = intval($_GET['id'] ?? 0);
                $this->getNetworkDetails($networkId);
                break;
            case 'validate-rpc':
                $rpcUrl = $_GET['rpc_url'] ?? '';
                $chainId = intval($_GET['chain_id'] ?? 0);
                $this->validateRpcConnection($rpcUrl, $chainId);
                break;
            case 'check-chain-id':
                $rpcUrl = $_GET['rpc_url'] ?? '';
                $this->checkChainId($rpcUrl);
                break;
            default:
                $this->sendError('Invalid action', 400);
        }
    }
    
    /**
     * معالجة طلبات POST
     */
    private function handlePost($action) {
        $input = json_decode(file_get_contents('php://input'), true);
        
        switch ($action) {
            case 'add':
                $this->addNetwork($input);
                break;
            case 'test-connection':
                $this->testNetworkConnection($input);
                break;
            default:
                $this->sendError('Invalid action', 400);
        }
    }
    
    /**
     * معالجة طلبات PUT
     */
    private function handlePut($action) {
        $input = json_decode(file_get_contents('php://input'), true);
        
        switch ($action) {
            case 'update':
                $networkId = intval($_GET['id'] ?? 0);
                $this->updateNetwork($networkId, $input);
                break;
            case 'toggle-status':
                $networkId = intval($_GET['id'] ?? 0);
                $this->toggleNetworkStatus($networkId);
                break;
            default:
                $this->sendError('Invalid action', 400);
        }
    }
    
    /**
     * معالجة طلبات DELETE
     */
    private function handleDelete($action) {
        switch ($action) {
            case 'remove':
                $networkId = intval($_GET['id'] ?? 0);
                $this->removeNetwork($networkId);
                break;
            default:
                $this->sendError('Invalid action', 400);
        }
    }
    
    /**
     * الحصول على قائمة الشبكات مع اختبار الاتصال
     */
    private function getNetworksList() {
        $pdo = $this->db->getConnection();

        $stmt = $pdo->prepare("
            SELECT
                n.*,
                COUNT(DISTINCT t.id) as tokens_count,
                COUNT(DISTINCT c.id) as contracts_count
            FROM supported_networks n
            LEFT JOIN supported_tokens t ON n.id = t.network_id AND t.is_active = 1
            LEFT JOIN enhanced_contracts c ON n.id = c.network_id AND c.is_active = 1
            GROUP BY n.id
            ORDER BY n.is_testnet ASC, n.network_name ASC
        ");

        $stmt->execute();
        $networks = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // تحويل البيانات للتنسيق المطلوب واختبار الاتصال
        foreach ($networks as &$network) {
            $network['id'] = (int)$network['id'];
            $network['chain_id'] = (int)$network['chain_id'];
            $network['is_testnet'] = (bool)$network['is_testnet'];
            $network['is_active'] = (bool)$network['is_active'];
            $network['gas_price_gwei'] = (float)$network['gas_price_gwei'];
            $network['block_time_seconds'] = (int)$network['block_time_seconds'];
            $network['confirmation_blocks'] = (int)$network['confirmation_blocks'];
            $network['tokens_count'] = (int)$network['tokens_count'];
            $network['contracts_count'] = (int)$network['contracts_count'];

            // اختبار الاتصال بـ RPC للشبكات النشطة
            if ($network['is_active']) {
                $rpcTest = $this->testRpcConnection($network['rpc_url'], $network['chain_id']);
                $network['rpc_status'] = $rpcTest['success'] ? 'connected' : 'failed';
                $network['rpc_response_time'] = $rpcTest['response_time'] ?? null;
                $network['rpc_error'] = $rpcTest['success'] ? null : $rpcTest['error'];

                // اختبار العقود المنشورة إذا كانت BSC Testnet
                if ($network['chain_id'] == 97) {
                    $network['deployed_contracts'] = $this->testDeployedContracts($network['id']);
                }
            } else {
                $network['rpc_status'] = 'inactive';
                $network['rpc_response_time'] = null;
                $network['rpc_error'] = null;
            }
        }

        $this->sendSuccess([
            'networks' => $networks,
            'total' => count($networks),
            'active_count' => count(array_filter($networks, function($n) { return $n['is_active']; })),
            'testnet_count' => count(array_filter($networks, function($n) { return $n['is_testnet']; })),
            'connected_count' => count(array_filter($networks, function($n) { return $n['rpc_status'] === 'connected'; }))
        ]);
    }

    /**
     * اختبار العقود المنشورة
     */
    private function testDeployedContracts($networkId) {
        try {
            $pdo = $this->db->getConnection();
            $stmt = $pdo->prepare("
                SELECT contract_type, contract_address, contract_version, is_active
                FROM enhanced_contracts
                WHERE network_id = ? AND is_active = TRUE
                ORDER BY contract_type ASC
            ");
            $stmt->execute([$networkId]);
            $contracts = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $contractsStatus = [];
            foreach ($contracts as $contract) {
                $contractsStatus[] = [
                    'type' => $contract['contract_type'],
                    'address' => $contract['contract_address'],
                    'version' => $contract['contract_version'],
                    'status' => 'deployed',
                    'verified' => true
                ];
            }

            return $contractsStatus;

        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * الحصول على تفاصيل شبكة محددة
     */
    private function getNetworkDetails($networkId) {
        if ($networkId <= 0) {
            $this->sendError('Invalid network ID', 400);
            return;
        }
        
        $pdo = $this->db->getConnection();
        
        // جلب بيانات الشبكة
        $stmt = $pdo->prepare("
            SELECT * FROM supported_networks WHERE id = ?
        ");
        $stmt->execute([$networkId]);
        $network = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$network) {
            $this->sendError('Network not found', 404);
            return;
        }
        
        // جلب العملات المرتبطة
        $stmt = $pdo->prepare("
            SELECT * FROM supported_tokens 
            WHERE network_id = ? 
            ORDER BY is_stablecoin DESC, token_symbol ASC
        ");
        $stmt->execute([$networkId]);
        $tokens = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // جلب العقود المرتبطة
        $stmt = $pdo->prepare("
            SELECT * FROM enhanced_contracts 
            WHERE network_id = ? 
            ORDER BY contract_type ASC
        ");
        $stmt->execute([$networkId]);
        $contracts = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // جلب الإعدادات المتقدمة
        $stmt = $pdo->prepare("
            SELECT * FROM network_settings 
            WHERE network_id = ? AND is_active = 1
            ORDER BY setting_key ASC
        ");
        $stmt->execute([$networkId]);
        $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $this->sendSuccess([
            'network' => $network,
            'tokens' => $tokens,
            'contracts' => $contracts,
            'settings' => $settings,
            'stats' => [
                'tokens_count' => count($tokens),
                'active_tokens' => count(array_filter($tokens, function($t) { return $t['is_active']; })),
                'stablecoins_count' => count(array_filter($tokens, function($t) { return $t['is_stablecoin']; })),
                'contracts_count' => count($contracts),
                'active_contracts' => count(array_filter($contracts, function($c) { return $c['is_active']; }))
            ]
        ]);
    }
    
    /**
     * التحقق من اتصال RPC
     */
    private function validateRpcConnection($rpcUrl, $expectedChainId = null) {
        if (empty($rpcUrl)) {
            $this->sendError('RPC URL is required', 400);
            return;
        }
        
        try {
            $result = $this->testRpcConnection($rpcUrl, $expectedChainId);
            $this->sendSuccess($result);
        } catch (Exception $e) {
            $this->sendError('RPC validation failed: ' . $e->getMessage(), 400);
        }
    }
    
    /**
     * فحص Chain ID من RPC
     */
    private function checkChainId($rpcUrl) {
        if (empty($rpcUrl)) {
            $this->sendError('RPC URL is required', 400);
            return;
        }
        
        try {
            $chainId = $this->getRpcChainId($rpcUrl);
            $this->sendSuccess([
                'chain_id' => $chainId,
                'rpc_url' => $rpcUrl,
                'status' => 'success'
            ]);
        } catch (Exception $e) {
            $this->sendError('Failed to get chain ID: ' . $e->getMessage(), 400);
        }
    }
    
    /**
     * إضافة شبكة جديدة
     */
    private function addNetwork($input) {
        // التحقق من البيانات المطلوبة
        $required = ['network_name', 'network_symbol', 'chain_id', 'rpc_url', 'explorer_url'];
        foreach ($required as $field) {
            if (empty($input[$field])) {
                $this->sendError("Field '$field' is required", 400);
                return;
            }
        }

        $pdo = $this->db->getConnection();

        try {
            // التحقق من عدم تكرار Chain ID
            $stmt = $pdo->prepare("SELECT id FROM supported_networks WHERE chain_id = ?");
            $stmt->execute([$input['chain_id']]);
            if ($stmt->fetch()) {
                $this->sendError('Chain ID already exists', 409);
                return;
            }

            // اختبار الاتصال بـ RPC
            $rpcTest = $this->testRpcConnection($input['rpc_url'], $input['chain_id']);
            if (!$rpcTest['success']) {
                $this->sendError('RPC connection failed: ' . $rpcTest['error'], 400);
                return;
            }

            // إدراج الشبكة الجديدة
            $stmt = $pdo->prepare("
                INSERT INTO supported_networks (
                    network_name, network_symbol, chain_id, rpc_url, explorer_url,
                    is_testnet, is_active, gas_price_gwei, block_time_seconds, confirmation_blocks,
                    created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
            ");

            $stmt->execute([
                $input['network_name'],
                $input['network_symbol'],
                $input['chain_id'],
                $input['rpc_url'],
                $input['explorer_url'],
                $input['is_testnet'] ?? false,
                $input['is_active'] ?? true,
                $input['gas_price_gwei'] ?? 1.0,
                $input['block_time_seconds'] ?? 12,
                $input['confirmation_blocks'] ?? 12
            ]);

            $networkId = $pdo->lastInsertId();

            // تسجيل العملية في سجل التحديثات
            $this->logNetworkUpdate('network_added', $networkId, null, null, $input);

            $this->sendSuccess([
                'network_id' => $networkId,
                'message' => 'Network added successfully',
                'rpc_test' => $rpcTest
            ]);

        } catch (Exception $e) {
            $this->sendError('Failed to add network: ' . $e->getMessage(), 500);
        }
    }

    /**
     * تحديث شبكة موجودة
     */
    private function updateNetwork($networkId, $input) {
        if ($networkId <= 0) {
            $this->sendError('Invalid network ID', 400);
            return;
        }

        $pdo = $this->db->getConnection();

        try {
            // جلب البيانات الحالية
            $stmt = $pdo->prepare("SELECT * FROM supported_networks WHERE id = ?");
            $stmt->execute([$networkId]);
            $currentData = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$currentData) {
                $this->sendError('Network not found', 404);
                return;
            }

            // التحقق من Chain ID إذا تم تغييره
            if (isset($input['chain_id']) && $input['chain_id'] != $currentData['chain_id']) {
                $stmt = $pdo->prepare("SELECT id FROM supported_networks WHERE chain_id = ? AND id != ?");
                $stmt->execute([$input['chain_id'], $networkId]);
                if ($stmt->fetch()) {
                    $this->sendError('Chain ID already exists', 409);
                    return;
                }
            }

            // اختبار RPC إذا تم تغييره
            if (isset($input['rpc_url']) && $input['rpc_url'] != $currentData['rpc_url']) {
                $chainId = $input['chain_id'] ?? $currentData['chain_id'];
                $rpcTest = $this->testRpcConnection($input['rpc_url'], $chainId);
                if (!$rpcTest['success']) {
                    $this->sendError('RPC connection failed: ' . $rpcTest['error'], 400);
                    return;
                }
            }

            // تحديث البيانات
            $updateFields = [];
            $updateValues = [];

            $allowedFields = [
                'network_name', 'network_symbol', 'chain_id', 'rpc_url', 'explorer_url',
                'is_testnet', 'is_active', 'gas_price_gwei', 'block_time_seconds', 'confirmation_blocks'
            ];

            foreach ($allowedFields as $field) {
                if (isset($input[$field])) {
                    $updateFields[] = "$field = ?";
                    $updateValues[] = $input[$field];
                }
            }

            if (empty($updateFields)) {
                $this->sendError('No fields to update', 400);
                return;
            }

            $updateValues[] = $networkId;

            $stmt = $pdo->prepare("
                UPDATE supported_networks
                SET " . implode(', ', $updateFields) . ", updated_at = NOW()
                WHERE id = ?
            ");

            $stmt->execute($updateValues);

            // تسجيل العملية في سجل التحديثات
            $this->logNetworkUpdate('network_updated', $networkId, null, $currentData, $input);

            $this->sendSuccess([
                'network_id' => $networkId,
                'message' => 'Network updated successfully',
                'updated_fields' => array_keys($input)
            ]);

        } catch (Exception $e) {
            $this->sendError('Failed to update network: ' . $e->getMessage(), 500);
        }
    }

    /**
     * تبديل حالة الشبكة (تفعيل/إلغاء تفعيل)
     */
    private function toggleNetworkStatus($networkId) {
        if ($networkId <= 0) {
            $this->sendError('Invalid network ID', 400);
            return;
        }

        $pdo = $this->db->getConnection();

        try {
            $stmt = $pdo->prepare("
                UPDATE supported_networks
                SET is_active = NOT is_active, updated_at = NOW()
                WHERE id = ?
            ");

            $stmt->execute([$networkId]);

            if ($stmt->rowCount() === 0) {
                $this->sendError('Network not found', 404);
                return;
            }

            // جلب الحالة الجديدة
            $stmt = $pdo->prepare("SELECT is_active FROM supported_networks WHERE id = ?");
            $stmt->execute([$networkId]);
            $newStatus = $stmt->fetchColumn();

            $this->sendSuccess([
                'network_id' => $networkId,
                'new_status' => (bool)$newStatus,
                'message' => $newStatus ? 'Network activated' : 'Network deactivated'
            ]);

        } catch (Exception $e) {
            $this->sendError('Failed to toggle network status: ' . $e->getMessage(), 500);
        }
    }

    /**
     * إرسال استجابة نجاح
     */
    private function sendSuccess($data, $statusCode = 200) {
        http_response_code($statusCode);
        echo json_encode([
            'success' => true,
            'data' => $data,
            'timestamp' => date('Y-m-d H:i:s')
        ], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        exit();
    }
    
    /**
     * حذف شبكة
     */
    private function removeNetwork($networkId) {
        if ($networkId <= 0) {
            $this->sendError('Invalid network ID', 400);
            return;
        }

        $pdo = $this->db->getConnection();

        try {
            // التحقق من وجود عملات أو عقود مرتبطة
            $stmt = $pdo->prepare("
                SELECT
                    (SELECT COUNT(*) FROM supported_tokens WHERE network_id = ?) as tokens_count,
                    (SELECT COUNT(*) FROM enhanced_contracts WHERE network_id = ?) as contracts_count
            ");
            $stmt->execute([$networkId, $networkId]);
            $counts = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($counts['tokens_count'] > 0 || $counts['contracts_count'] > 0) {
                $this->sendError(
                    'Cannot delete network with associated tokens or contracts. ' .
                    "Tokens: {$counts['tokens_count']}, Contracts: {$counts['contracts_count']}",
                    409
                );
                return;
            }

            // حذف الشبكة
            $stmt = $pdo->prepare("DELETE FROM supported_networks WHERE id = ?");
            $stmt->execute([$networkId]);

            if ($stmt->rowCount() === 0) {
                $this->sendError('Network not found', 404);
                return;
            }

            // تسجيل العملية في سجل التحديثات
            $this->logNetworkUpdate('network_removed', $networkId, null, null, null);

            $this->sendSuccess([
                'network_id' => $networkId,
                'message' => 'Network removed successfully'
            ]);

        } catch (Exception $e) {
            $this->sendError('Failed to remove network: ' . $e->getMessage(), 500);
        }
    }

    /**
     * اختبار اتصال الشبكة
     */
    private function testNetworkConnection($input) {
        $rpcUrl = $input['rpc_url'] ?? '';
        $expectedChainId = $input['chain_id'] ?? null;

        if (empty($rpcUrl)) {
            $this->sendError('RPC URL is required', 400);
            return;
        }

        try {
            $result = $this->testRpcConnection($rpcUrl, $expectedChainId);
            $this->sendSuccess($result);
        } catch (Exception $e) {
            $this->sendError('Connection test failed: ' . $e->getMessage(), 400);
        }
    }

    /**
     * اختبار اتصال RPC
     */
    private function testRpcConnection($rpcUrl, $expectedChainId = null) {
        $context = stream_context_create([
            'http' => [
                'method' => 'POST',
                'header' => 'Content-Type: application/json',
                'content' => json_encode([
                    'jsonrpc' => '2.0',
                    'method' => 'eth_chainId',
                    'params' => [],
                    'id' => 1
                ]),
                'timeout' => 10
            ]
        ]);

        $response = file_get_contents($rpcUrl, false, $context);

        if ($response === false) {
            throw new Exception('Failed to connect to RPC endpoint');
        }

        $data = json_decode($response, true);

        if (!$data || isset($data['error'])) {
            throw new Exception('RPC returned error: ' . ($data['error']['message'] ?? 'Unknown error'));
        }

        $chainId = hexdec($data['result']);

        $result = [
            'success' => true,
            'chain_id' => $chainId,
            'rpc_url' => $rpcUrl,
            'response_time' => 'Fast', // يمكن تحسينه لقياس الوقت الفعلي
            'status' => 'Connected'
        ];

        if ($expectedChainId !== null && $chainId !== $expectedChainId) {
            $result['warning'] = "Chain ID mismatch. Expected: $expectedChainId, Got: $chainId";
        }

        return $result;
    }

    /**
     * الحصول على Chain ID من RPC
     */
    private function getRpcChainId($rpcUrl) {
        $context = stream_context_create([
            'http' => [
                'method' => 'POST',
                'header' => 'Content-Type: application/json',
                'content' => json_encode([
                    'jsonrpc' => '2.0',
                    'method' => 'eth_chainId',
                    'params' => [],
                    'id' => 1
                ]),
                'timeout' => 10
            ]
        ]);

        $response = file_get_contents($rpcUrl, false, $context);

        if ($response === false) {
            throw new Exception('Failed to connect to RPC endpoint');
        }

        $data = json_decode($response, true);

        if (!$data || isset($data['error'])) {
            throw new Exception('RPC returned error: ' . ($data['error']['message'] ?? 'Unknown error'));
        }

        return hexdec($data['result']);
    }

    /**
     * تسجيل تحديث الشبكة في السجل
     */
    private function logNetworkUpdate($updateType, $networkId, $tokenId, $oldData, $newData) {
        try {
            $pdo = $this->db->getConnection();

            $stmt = $pdo->prepare("
                INSERT INTO network_token_updates (
                    update_type, network_id, token_id, admin_id, old_data, new_data,
                    update_source, notes, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
            ");

            $stmt->execute([
                $updateType,
                $networkId,
                $tokenId,
                1, // TODO: الحصول على معرف المدير الحقيقي من الجلسة
                $oldData ? json_encode($oldData) : null,
                $newData ? json_encode($newData) : null,
                'manual',
                "Network $updateType operation"
            ]);

        } catch (Exception $e) {
            // تسجيل الخطأ لكن لا نوقف العملية
            error_log("Failed to log network update: " . $e->getMessage());
        }
    }

    /**
     * إرسال استجابة خطأ
     */
    private function sendError($message, $statusCode = 400) {
        http_response_code($statusCode);
        echo json_encode([
            'success' => false,
            'error' => $message,
            'timestamp' => date('Y-m-d H:i:s')
        ], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        exit();
    }
}

// تشغيل API
try {
    $api = new NetworkManagementAPI();
    $api->handleRequest();
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Internal server error: ' . $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
}
?>
