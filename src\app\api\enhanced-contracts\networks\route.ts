/**
 * Next.js API Route Proxy for Networks
 * بروكسي Next.js للشبكات
 */

import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // استخراج المعاملات من URL
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || '';
    const active = searchParams.get('active') || '';
    const testnet = searchParams.get('testnet') || '';

    // بناء URL للـ PHP API - استخدام network-management.php مع action=list
    const phpApiUrl = new URL('http://localhost/ikaros-p2p/api/enhanced-contracts/network-management.php');
    phpApiUrl.searchParams.set('action', 'list');
    if (active) phpApiUrl.searchParams.set('active', active);
    if (testnet) phpApiUrl.searchParams.set('testnet', testnet);

    console.log('🔄 Proxying networks request to:', phpApiUrl.toString());

    // إرسال الطلب إلى PHP API
    const response = await fetch(phpApiUrl.toString(), {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    console.log('📡 Networks API Response Status:', response.status);

    if (!response.ok) {
      throw new Error(`Networks API returned ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    console.log('📊 Networks API Data:', data);

    // إرجاع البيانات مع headers صحيحة
    return NextResponse.json(data, {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });

  } catch (error) {
    console.error('❌ Networks API Error:', error);
    
    // إرجاع بيانات احتياطية في حالة الخطأ
    const fallbackData = {
      success: false,
      error: 'فشل في الاتصال بـ API الشبكات',
      error_en: 'Failed to connect to Networks API',
      data: {
        networks: [],
        count: 0,
        filters: {
          active_only: true,
          testnet_only: null
        },
        summary: {
          total_networks: 0,
          active_networks: 0,
          testnet_networks: 0,
          mainnet_networks: 0
        }
      },
      timestamp: new Date().toISOString(),
      source: 'fallback'
    };

    return NextResponse.json(fallbackData, {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    console.log('🔄 Proxying networks POST request to PHP API');

    // إرسال الطلب إلى PHP API
    const response = await fetch('http://localhost/ikaros-p2p/api/enhanced-contracts/network-management.php', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify(body),
    });

    console.log('📡 Networks POST API Response Status:', response.status);

    if (!response.ok) {
      throw new Error(`Networks POST API returned ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    console.log('📊 Networks POST API Data:', data);

    return NextResponse.json(data, {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });

  } catch (error) {
    console.error('❌ Networks POST API Error:', error);
    
    return NextResponse.json({
      success: false,
      error: 'فشل في معالجة طلب POST للشبكات',
      error_en: 'Failed to process Networks POST request',
      timestamp: new Date().toISOString()
    }, {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });
  }
}

export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
