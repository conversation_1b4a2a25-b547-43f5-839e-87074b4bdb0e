<?php
/**
 * Database Sync API for Enhanced Contracts
 * مزامنة قاعدة البيانات للعقود المحسنة
 * 
 * يدير تخزين وجلب بيانات الشبكات والعملات من قاعدة البيانات
 */

// إعدادات CORS
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// تضمين ملف قاعدة البيانات
require_once __DIR__ . '/../config/database.php';

// دالة مساعدة لإرسال JSON
function sendJsonResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    echo json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    exit();
}

try {
    // الاتصال بقاعدة البيانات
    $database = new Database();
    $pdo = $database->getConnection();
    
    $method = $_SERVER['REQUEST_METHOD'];
    $action = $_GET['action'] ?? '';

    switch ($method) {
        case 'GET':
            switch ($action) {
                case 'networks':
                    // جلب الشبكات من قاعدة البيانات
                    $activeOnly = isset($_GET['active']) && $_GET['active'] == '1';

                    // استعلام محسن مع JOIN لتجنب N+1 query problem
                    $sql = "SELECT sn.*,
                                   COALESCE(token_counts.token_count, 0) as supported_tokens_count
                            FROM supported_networks sn
                            LEFT JOIN (
                                SELECT network_id, COUNT(*) as token_count
                                FROM supported_tokens
                                WHERE is_active = 1
                                GROUP BY network_id
                            ) token_counts ON sn.id = token_counts.network_id";

                    if ($activeOnly) {
                        $sql .= " WHERE sn.is_active = 1";
                    }
                    $sql .= " ORDER BY sn.is_active DESC, sn.network_name ASC";

                    $stmt = $pdo->prepare($sql);
                    $stmt->execute();
                    $networks = $stmt->fetchAll(PDO::FETCH_ASSOC);

                    // إضافة إحصائيات لكل شبكة (بدون استعلامات إضافية)
                    foreach ($networks as &$network) {
                        $network['health_status'] = $network['is_active'] ? 'excellent' : 'inactive';
                        $network['daily_transactions'] = rand(10000, 50000); // سيتم ربطها بـ BSCScan لاحقاً
                        $network['total_value_locked'] = number_format(rand(1000000, 5000000), 2);
                    }
                    
                    sendJsonResponse([
                        'success' => true,
                        'data' => [
                            'networks' => $networks,
                            'count' => count($networks),
                            'filters' => [
                                'active_only' => $activeOnly,
                                'testnet_only' => null
                            ],
                            'summary' => [
                                'total_networks' => count($networks),
                                'active_networks' => count(array_filter($networks, function($n) { return $n['is_active']; })),
                                'testnet_networks' => count(array_filter($networks, function($n) { return $n['is_testnet']; })),
                                'mainnet_networks' => count(array_filter($networks, function($n) { return !$n['is_testnet']; }))
                            ]
                        ],
                        'message' => 'تم جلب الشبكات من قاعدة البيانات بنجاح',
                        'message_en' => 'Networks retrieved from database successfully',
                        'timestamp' => date('Y-m-d H:i:s'),
                        'source' => 'database'
                    ]);
                    break;

                case 'tokens':
                    // جلب العملات من قاعدة البيانات
                    $activeOnly = isset($_GET['active']) && $_GET['active'] == '1';
                    $networkId = $_GET['network_id'] ?? null;
                    
                    $sql = "SELECT st.*, sn.network_name, sn.chain_id, sn.network_symbol 
                            FROM supported_tokens st 
                            JOIN supported_networks sn ON st.network_id = sn.id";
                    
                    $conditions = [];
                    $params = [];
                    
                    if ($activeOnly) {
                        $conditions[] = "st.is_active = 1";
                    }
                    if ($networkId) {
                        $conditions[] = "st.network_id = ?";
                        $params[] = $networkId;
                    }
                    
                    if (!empty($conditions)) {
                        $sql .= " WHERE " . implode(" AND ", $conditions);
                    }
                    
                    $sql .= " ORDER BY st.is_active DESC, sn.network_name ASC, st.token_symbol ASC";
                    
                    $stmt = $pdo->prepare($sql);
                    $stmt->execute($params);
                    $tokens = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    
                    // إضافة بيانات السوق (ستأتي من APIs خارجية لاحقاً)
                    foreach ($tokens as &$token) {
                        $token['current_price_usd'] = $token['is_stablecoin'] ? '1.00' : number_format(rand(100, 50000) / 100, 2);
                        $token['market_cap'] = number_format(rand(10000000, 100000000), 2);
                        $token['volume_24h'] = number_format(rand(1000000, 10000000), 2);
                        $token['price_change_24h'] = number_format((rand(-500, 500) / 100), 2);
                        $token['total_offers'] = rand(10, 100);
                        $token['total_trades'] = rand(50, 500);
                        $token['avg_trade_size'] = number_format(rand(100, 1000), 2);
                    }
                    
                    sendJsonResponse([
                        'success' => true,
                        'data' => [
                            'tokens' => $tokens,
                            'count' => count($tokens),
                            'filters' => [
                                'network_id' => $networkId,
                                'active_only' => $activeOnly,
                                'stablecoin_only' => false
                            ],
                            'summary' => [
                                'total_tokens' => count($tokens),
                                'active_tokens' => count(array_filter($tokens, function($t) { return $t['is_active']; })),
                                'stablecoins' => count(array_filter($tokens, function($t) { return $t['is_stablecoin']; })),
                                'total_market_cap' => array_sum(array_column($tokens, 'market_cap')),
                                'total_volume_24h' => array_sum(array_column($tokens, 'volume_24h'))
                            ]
                        ],
                        'message' => 'تم جلب العملات من قاعدة البيانات بنجاح',
                        'message_en' => 'Tokens retrieved from database successfully',
                        'timestamp' => date('Y-m-d H:i:s'),
                        'source' => 'database'
                    ]);
                    break;

                default:
                    sendJsonResponse([
                        'success' => false,
                        'error' => 'إجراء غير مدعوم',
                        'error_en' => 'Unsupported action',
                        'available_actions' => ['networks', 'tokens'],
                        'timestamp' => date('Y-m-d H:i:s')
                    ], 400);
                    break;
            }
            break;

        case 'POST':
            // حفظ/تحديث البيانات في قاعدة البيانات
            $input = json_decode(file_get_contents('php://input'), true);
            
            switch ($action) {
                case 'sync-networks':
                    // مزامنة الشبكات مع قاعدة البيانات
                    $networks = $input['networks'] ?? [];
                    $syncedCount = 0;
                    
                    foreach ($networks as $network) {
                        // التحقق من وجود الشبكة
                        $checkStmt = $pdo->prepare("SELECT id FROM supported_networks WHERE chain_id = ?");
                        $checkStmt->execute([$network['chain_id']]);
                        $existing = $checkStmt->fetch();
                        
                        if ($existing) {
                            // تحديث الشبكة الموجودة
                            $updateStmt = $pdo->prepare("
                                UPDATE supported_networks SET 
                                    network_name = ?, network_symbol = ?, rpc_url = ?, 
                                    explorer_url = ?, is_testnet = ?, is_active = ?, 
                                    gas_price_gwei = ?, block_time_seconds = ?, 
                                    confirmation_blocks = ?, updated_at = NOW()
                                WHERE chain_id = ?
                            ");
                            $updateStmt->execute([
                                $network['network_name'],
                                $network['network_symbol'],
                                $network['rpc_url'],
                                $network['explorer_url'],
                                $network['is_testnet'] ? 1 : 0,
                                $network['is_active'] ? 1 : 0,
                                $network['gas_price_gwei'],
                                $network['block_time_seconds'],
                                $network['confirmation_blocks'],
                                $network['chain_id']
                            ]);
                        } else {
                            // إضافة شبكة جديدة
                            $insertStmt = $pdo->prepare("
                                INSERT INTO supported_networks 
                                (network_name, network_symbol, chain_id, rpc_url, explorer_url, 
                                 is_testnet, is_active, gas_price_gwei, block_time_seconds, 
                                 confirmation_blocks, created_at, updated_at) 
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
                            ");
                            $insertStmt->execute([
                                $network['network_name'],
                                $network['network_symbol'],
                                $network['chain_id'],
                                $network['rpc_url'],
                                $network['explorer_url'],
                                $network['is_testnet'] ? 1 : 0,
                                $network['is_active'] ? 1 : 0,
                                $network['gas_price_gwei'],
                                $network['block_time_seconds'],
                                $network['confirmation_blocks']
                            ]);
                        }
                        $syncedCount++;
                    }
                    
                    sendJsonResponse([
                        'success' => true,
                        'message' => "تم مزامنة {$syncedCount} شبكة بنجاح",
                        'message_en' => "{$syncedCount} networks synced successfully",
                        'synced_count' => $syncedCount,
                        'timestamp' => date('Y-m-d H:i:s')
                    ]);
                    break;

                case 'sync-tokens':
                    // مزامنة العملات مع قاعدة البيانات
                    $tokens = $input['tokens'] ?? [];
                    $syncedCount = 0;
                    $errors = [];

                    foreach ($tokens as $token) {
                        try {
                            // التحقق من الحقول المطلوبة
                            if (empty($token['token_address']) || empty($token['network_id'])) {
                                $errors[] = "عنوان العملة أو معرف الشبكة مفقود: " . json_encode($token);
                                continue;
                            }

                            // التحقق من وجود العملة
                            $checkStmt = $pdo->prepare("SELECT id FROM supported_tokens WHERE token_address = ? AND network_id = ?");
                            $checkStmt->execute([$token['token_address'], $token['network_id']]);
                            $existing = $checkStmt->fetch();
                        
                        if ($existing) {
                            // تحديث العملة الموجودة
                            $updateStmt = $pdo->prepare("
                                UPDATE supported_tokens SET 
                                    token_symbol = ?, token_name = ?, decimals = ?, 
                                    is_stablecoin = ?, is_active = ?, coingecko_id = ?,
                                    min_trade_amount = ?, max_trade_amount = ?, 
                                    daily_volume_limit = ?, platform_fee_rate = ?, 
                                    updated_at = NOW()
                                WHERE token_address = ? AND network_id = ?
                            ");
                            $updateStmt->execute([
                                $token['token_symbol'],
                                $token['token_name'],
                                $token['decimals'],
                                $token['is_stablecoin'] ? 1 : 0,
                                $token['is_active'] ? 1 : 0,
                                $token['coingecko_id'],
                                $token['min_trade_amount'],
                                $token['max_trade_amount'],
                                $token['daily_volume_limit'],
                                $token['platform_fee_rate'],
                                $token['token_address'],
                                $token['network_id']
                            ]);
                        } else {
                            // إضافة عملة جديدة
                            $insertStmt = $pdo->prepare("
                                INSERT INTO supported_tokens 
                                (network_id, token_address, token_symbol, token_name, decimals, 
                                 is_stablecoin, is_active, coingecko_id, min_trade_amount, 
                                 max_trade_amount, daily_volume_limit, platform_fee_rate, 
                                 created_at, updated_at) 
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
                            ");
                            $insertStmt->execute([
                                $token['network_id'],
                                $token['token_address'],
                                $token['token_symbol'],
                                $token['token_name'],
                                $token['decimals'],
                                $token['is_stablecoin'] ? 1 : 0,
                                $token['is_active'] ? 1 : 0,
                                $token['coingecko_id'],
                                $token['min_trade_amount'],
                                $token['max_trade_amount'],
                                $token['daily_volume_limit'],
                                $token['platform_fee_rate']
                            ]);
                        }
                        $syncedCount++;
                        } catch (Exception $e) {
                            $errors[] = "خطأ في معالجة العملة: " . $e->getMessage();
                        }
                    }

                    if (!empty($errors)) {
                        sendJsonResponse([
                            'success' => false,
                            'error' => 'فشل في مزامنة بعض العملات',
                            'errors' => $errors,
                            'synced_count' => $syncedCount,
                            'timestamp' => date('Y-m-d H:i:s')
                        ], 400);
                    } else {
                        sendJsonResponse([
                            'success' => true,
                            'message' => "تم مزامنة {$syncedCount} عملة بنجاح",
                            'message_en' => "{$syncedCount} tokens synced successfully",
                            'synced_count' => $syncedCount,
                            'timestamp' => date('Y-m-d H:i:s')
                        ]);
                    }
                    break;

                default:
                    sendJsonResponse([
                        'success' => false,
                        'error' => 'إجراء غير مدعوم',
                        'error_en' => 'Unsupported action',
                        'available_actions' => ['sync-networks', 'sync-tokens'],
                        'timestamp' => date('Y-m-d H:i:s')
                    ], 400);
                    break;
            }
            break;

        default:
            sendJsonResponse([
                'success' => false,
                'error' => 'طريقة طلب غير مدعومة',
                'error_en' => 'Method not allowed',
                'allowed_methods' => ['GET', 'POST'],
                'timestamp' => date('Y-m-d H:i:s')
            ], 405);
            break;
    }

} catch (Exception $e) {
    sendJsonResponse([
        'success' => false,
        'error' => 'خطأ في قاعدة البيانات: ' . $e->getMessage(),
        'error_en' => 'Database error: ' . $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ], 500);
}
?>
