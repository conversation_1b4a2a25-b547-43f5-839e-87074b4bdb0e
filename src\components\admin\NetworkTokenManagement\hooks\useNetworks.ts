/**
 * Hook لإدارة الشبكات
 * Networks management hook
 */

import { useState, useCallback } from 'react';
import { Network, NetworkStats, DefaultNetworkData } from '../types';

export const useNetworks = () => {
  const [networks, setNetworks] = useState<Network[]>([]);
  const [pendingNetworks, setPendingNetworks] = useState<Network[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // إحصائيات الشبكات
  const getNetworkStats = useCallback((networkList: Network[]): NetworkStats => {
    return {
      total: networkList.length,
      active: networkList.filter(n => n.status === 'active').length,
      inactive: networkList.filter(n => n.status === 'inactive').length,
      testnet: networkList.filter(n => n.type === 'testnet').length,
      mainnet: networkList.filter(n => n.type === 'mainnet').length,
      syncing: networkList.filter(n => n.status === 'syncing').length,
    };
  }, []);

  // تحويل البيانات من API إلى تنسيق المكون
  const formatNetworkData = useCallback((networkData: DefaultNetworkData): Network => {
    return {
      id: networkData.id.toString(),
      name: networkData.network_name,
      chainId: parseInt(networkData.chain_id.toString()),
      type: networkData.is_testnet ? 'testnet' : 'mainnet',
      status: networkData.is_active ? 'active' : 'inactive',
      rpcUrl: networkData.rpc_url,
      explorerUrl: networkData.explorer_url,
      nativeCurrency: {
        name: networkData.network_name,
        symbol: networkData.network_symbol,
        decimals: 18
      },
      blockTime: networkData.block_time_seconds || 3,
      gasPrice: parseFloat(networkData.gas_price_gwei.toString()) || 5,
      latency: 100,
      uptime: 99.5,
      blockHeight: 0,
      nodeCount: 1,
      isEnabled: networkData.is_active,
      lastSync: networkData.updated_at || networkData.created_at || new Date().toISOString(),
      totalTransactions: networkData.daily_transactions || 0,
      dailyTransactions: networkData.daily_transactions || 0
    };
  }, []);

  // جلب الشبكات من قاعدة البيانات
  const fetchNetworksFromDatabase = useCallback(async (): Promise<Network[]> => {
    try {
      console.log('🔄 Fetching networks from database...');

      // إضافة timeout للطلب
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 ثواني timeout

      const response = await fetch('/api/enhanced-contracts/database-sync?action=networks&active=1', {
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        }
      });

      clearTimeout(timeoutId);

      console.log('📡 Database response status:', response.status);
      console.log('📡 Database response headers:', response.headers.get('content-type'));

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Database API Error Response:', errorText);
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // التحقق من نوع المحتوى
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        const responseText = await response.text();
        console.error('❌ Non-JSON response received:', responseText.substring(0, 200));
        throw new Error('الاستجابة ليست JSON صالح');
      }

      const data = await response.json();

      if (data.success && data.data.networks.length > 0) {
        const formattedNetworks = data.data.networks.map(formatNetworkData);
        console.log('✅ Networks loaded from database:', formattedNetworks);
        return formattedNetworks;
      }
      return [];
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        console.error('⏰ Database request timeout');
      } else {
        console.error('Error fetching networks from database:', error);
      }
      return [];
    }
  }, [formatNetworkData]);

  // بيانات افتراضية للشبكات
  const getDefaultNetworks = useCallback((): Network[] => {
    return [
      {
        id: '1',
        name: 'Ethereum Mainnet',
        chainId: 1,
        type: 'mainnet',
        status: 'active',
        rpcUrl: 'https://mainnet.infura.io/v3/',
        explorerUrl: 'https://etherscan.io',
        nativeCurrency: { name: 'Ethereum', symbol: 'ETH', decimals: 18 },
        blockTime: 12,
        gasPrice: 20,
        latency: 150,
        uptime: 99.9,
        blockHeight: 18000000,
        nodeCount: 5000,
        isEnabled: true,
        lastSync: new Date().toISOString(),
        totalTransactions: 1500000000,
        dailyTransactions: 1200000
      },
      {
        id: '56',
        name: 'BNB Smart Chain',
        chainId: 56,
        type: 'mainnet',
        status: 'active',
        rpcUrl: 'https://bsc-dataseed1.binance.org/',
        explorerUrl: 'https://bscscan.com',
        nativeCurrency: { name: 'BNB', symbol: 'BNB', decimals: 18 },
        blockTime: 3,
        gasPrice: 5,
        latency: 100,
        uptime: 99.8,
        blockHeight: 32000000,
        nodeCount: 100,
        isEnabled: true,
        lastSync: new Date().toISOString(),
        totalTransactions: 800000000,
        dailyTransactions: 800000
      }
    ];
  }, []);

  // جلب الشبكات من البلوك تشين
  const fetchNetworksFromBlockchain = useCallback(async (): Promise<Network[]> => {
    try {
      console.log('🔄 Fetching networks from blockchain...');

      // إضافة timeout للطلب
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 ثانية timeout

      const response = await fetch('/api/enhanced-contracts/networks?action=networks&active=1', {
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        }
      });

      clearTimeout(timeoutId);

      console.log('📡 Blockchain response status:', response.status);
      console.log('📡 Blockchain response headers:', response.headers.get('content-type'));

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Blockchain API Error Response:', errorText);
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        const responseText = await response.text();
        console.error('❌ Non-JSON response received:', responseText.substring(0, 200));
        throw new Error('الاستجابة ليست JSON صالح');
      }

      const data = await response.json();

      if (data.success && data.data && data.data.networks) {
        const formattedNetworks = data.data.networks.map(formatNetworkData);
        console.log('✅ Networks loaded from blockchain:', formattedNetworks);
        return formattedNetworks;
      }
      throw new Error(data.message || 'فشل في جلب الشبكات');
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        console.error('⏰ Blockchain request timeout');
        throw new Error('انتهت مهلة الاتصال بالبلوك تشين');
      } else {
        console.error('Error fetching networks from blockchain:', error);
        throw error;
      }
    }
  }, [formatNetworkData]);

  // حفظ الشبكات في قاعدة البيانات
  const saveNetworksToDatabase = useCallback(async (networksToSave: Network[]): Promise<void> => {
    try {
      console.log('💾 Saving networks to database...');
      
      const networksForAPI = networksToSave.map(network => ({
        id: network.id,
        network_name: network.name,
        chain_id: network.chainId,
        rpc_url: network.rpcUrl,
        explorer_url: network.explorerUrl,
        network_symbol: network.nativeCurrency?.symbol || 'ETH',
        is_testnet: network.type === 'testnet',
        is_active: network.isEnabled,
        block_time_seconds: network.blockTime,
        gas_price_gwei: network.gasPrice,
        confirmation_blocks: 12,
        daily_transactions: network.dailyTransactions
      }));

      const response = await fetch('/api/enhanced-contracts/database-sync?action=sync-networks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          networks: networksForAPI
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('✅ Networks saved to database:', result);
    } catch (error) {
      console.error('Error saving networks to database:', error);
      throw error;
    }
  }, []);

  // جلب الشبكات (من قاعدة البيانات أولاً، ثم من البلوك تشين)
  const fetchNetworks = useCallback(async (fromBlockchain = false) => {
    console.log('🔄 Starting fetchNetworks, fromBlockchain:', fromBlockchain);

    setIsLoading(true);
    setError(null);

    try {
      let networksData: Network[] = [];

      if (!fromBlockchain) {
        // محاولة جلب من قاعدة البيانات أولاً
        console.log('📊 Attempting to load from database...');
        try {
          networksData = await fetchNetworksFromDatabase();
        } catch (dbError) {
          console.warn('⚠️ Database fetch failed, will try blockchain:', dbError);
        }
      }

      // إذا لم توجد بيانات في قاعدة البيانات أو طلب تحديث من البلوك تشين
      if (networksData.length === 0 || fromBlockchain) {
        console.log('🔗 Loading from blockchain...');
        try {
          networksData = await fetchNetworksFromBlockchain();

          // حفظ البيانات في قاعدة البيانات (في الخلفية)
          if (networksData.length > 0) {
            saveNetworksToDatabase(networksData).catch(syncError => {
              console.warn('⚠️ Failed to sync networks to database:', syncError);
            });
          }
        } catch (blockchainError) {
          console.warn('⚠️ Blockchain fetch failed, using defaults:', blockchainError);
          networksData = getDefaultNetworks();
        }
      }

      // إذا لم نحصل على أي بيانات، استخدم البيانات الافتراضية
      if (networksData.length === 0) {
        networksData = getDefaultNetworks();
      }

      setNetworks(networksData);
      console.log('✅ Networks loaded successfully:', networksData.length);
    } catch (error) {
      console.error('❌ Error fetching networks:', error);
      setError('فشل في جلب بيانات الشبكات - سيتم استخدام البيانات الافتراضية');
      setNetworks(getDefaultNetworks());
    } finally {
      setIsLoading(false);
    }
  }, [fetchNetworksFromDatabase, fetchNetworksFromBlockchain, saveNetworksToDatabase, getDefaultNetworks]);

  // تحديث حالة شبكة معينة
  const toggleNetwork = useCallback(async (networkId: string) => {
    try {
      const network = networks.find(n => n.id === networkId);
      if (!network) return;

      const updatedNetwork: Network = {
        ...network,
        isEnabled: !network.isEnabled,
        status: (!network.isEnabled ? 'active' : 'inactive') as 'active' | 'inactive'
      };

      setNetworks(prev => prev.map(n => n.id === networkId ? updatedNetwork : n));
      console.log(`🔄 Network ${network.name} ${updatedNetwork.isEnabled ? 'enabled' : 'disabled'}`);
    } catch (error) {
      console.error('Error toggling network:', error);
      setError('فشل في تحديث حالة الشبكة');
    }
  }, [networks]);



  return {
    networks,
    pendingNetworks,
    isLoading,
    error,
    setNetworks,
    setPendingNetworks,
    setError,
    fetchNetworks,
    toggleNetwork,
    saveNetworksToDatabase,
    getNetworkStats,
    formatNetworkData
  };
};
