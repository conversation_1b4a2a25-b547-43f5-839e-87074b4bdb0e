'use client';

import React, { useState, useMemo } from 'react';
import {
  Server,
  Search,
  Filter,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  Eye,
  Settings,
  Wifi,
  WifiOff,
  Globe,
  TestTube,
  Activity,
  TrendingUp,
  RefreshCw
} from 'lucide-react';
import { useAdminTranslation } from '@/hooks/useAdminTranslation';
import { NetworkManagementProps, Network } from '../types';
import NetworkCard from './NetworkCard';
import StatsCards from './StatsCards';
import SearchFilter from './SearchFilter';
import BulkActions from './BulkActions';

export default function NetworkManagement({
  networks,
  pendingNetworks,
  isLoading,
  error,
  selectedItems,
  searchTerm,
  selectedFilter,
  onNetworkToggle,
  onItemAction,
  onSelectionChange,
  onSearchChange,
  onFilterChange,
  hasPendingChanges,
  isRefreshing,
  isSaving
}: NetworkManagementProps) {
  const { t, isRTL, getDirectionClasses, formatNumber } = useAdminTranslation();
  const dirClasses = getDirectionClasses();

  // استخدام البيانات المعلقة إذا كانت متوفرة، وإلا البيانات العادية
  const currentNetworks = hasPendingChanges && pendingNetworks.length > 0 ? pendingNetworks : networks;

  // تصفية الشبكات حسب البحث والفلتر
  const filteredNetworks = useMemo(() => {
    let filtered = currentNetworks;

    // تطبيق البحث
    if (searchTerm) {
      filtered = filtered.filter(network =>
        network.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        network.nativeCurrency.symbol.toLowerCase().includes(searchTerm.toLowerCase()) ||
        network.chainId.toString().includes(searchTerm)
      );
    }

    // تطبيق الفلتر
    switch (selectedFilter) {
      case 'active':
        filtered = filtered.filter(n => n.status === 'active');
        break;
      case 'inactive':
        filtered = filtered.filter(n => n.status === 'inactive');
        break;
      case 'testnet':
        filtered = filtered.filter(n => n.type === 'testnet');
        break;
      case 'mainnet':
        filtered = filtered.filter(n => n.type === 'mainnet');
        break;
      default:
        // 'all' - لا تطبق أي فلتر
        break;
    }

    return filtered;
  }, [currentNetworks, searchTerm, selectedFilter]);

  // إحصائيات الشبكات
  const networkStats = useMemo(() => {
    return {
      total: currentNetworks.length,
      active: currentNetworks.filter(n => n.status === 'active').length,
      inactive: currentNetworks.filter(n => n.status === 'inactive').length,
      testnet: currentNetworks.filter(n => n.type === 'testnet').length,
      mainnet: currentNetworks.filter(n => n.type === 'mainnet').length,
      syncing: currentNetworks.filter(n => n.status === 'syncing').length,
    };
  }, [currentNetworks]);

  // معالجة تحديد/إلغاء تحديد شبكة
  const handleNetworkSelection = (networkId: string, selected: boolean) => {
    if (selected) {
      onSelectionChange([...selectedItems, networkId]);
    } else {
      onSelectionChange(selectedItems.filter(id => id !== networkId));
    }
  };

  // معالجة تحديد/إلغاء تحديد الكل
  const handleSelectAll = () => {
    if (selectedItems.length === filteredNetworks.length) {
      onSelectionChange([]);
    } else {
      onSelectionChange(filteredNetworks.map(n => n.id));
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              {t('networks.loading')}
            </h3>
            <p className="text-gray-500 dark:text-gray-400">
              {t('networks.loadingDescription')}
            </p>
            <div className="mt-4 text-sm text-gray-400">
              {t('networks.loadingTimeout')}
            </div>
          </div>
        </div>

        {/* Skeleton loading للمحتوى */}
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-6">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-20 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
        <div className="flex items-center">
          <AlertTriangle className="w-5 h-5 text-red-500 mr-2" />
          <span className="text-red-700 dark:text-red-300">{error}</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* إشعار البيانات المعلقة */}
      {hasPendingChanges && pendingNetworks.length > 0 && (
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <RefreshCw className="w-5 h-5 text-green-500 mr-2" />
              <span className="text-green-700 dark:text-green-300 font-medium">
                {t('networks.pendingChanges')} ({pendingNetworks.length} {t('networks.networks')})
              </span>
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={() => onItemAction('save')}
                disabled={isSaving}
                className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700 disabled:opacity-50"
              >
                {isSaving ? t('common.saving') : t('common.save')}
              </button>
              <button
                onClick={() => onItemAction('cancel')}
                disabled={isSaving}
                className="px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-700 disabled:opacity-50"
              >
                {t('common.cancel')}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* إحصائيات الشبكات */}
      <StatsCards stats={networkStats} type="networks" isLoading={false} />

      {/* البحث والفلتر */}
      <SearchFilter
        searchTerm={searchTerm}
        selectedFilter={selectedFilter}
        onSearchChange={onSearchChange}
        onFilterChange={onFilterChange}
        type="networks"
      />

      {/* إجراءات مجمعة */}
      <BulkActions
        selectedCount={selectedItems.length}
        onAction={onItemAction}
        isVisible={selectedItems.length > 0}
        type="networks"
      />

      {/* جدول الشبكات */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
        {/* رأس الجدول */}
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <input
                type="checkbox"
                checked={selectedItems.length === filteredNetworks.length && filteredNetworks.length > 0}
                onChange={handleSelectAll}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="ml-3 text-sm font-medium text-gray-900 dark:text-white">
                {t('networks.selectAll')} ({filteredNetworks.length})
              </span>
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              {t('networks.showing')} {filteredNetworks.length} {t('networks.of')} {currentNetworks.length}
            </div>
          </div>
        </div>

        {/* قائمة الشبكات */}
        <div className="divide-y divide-gray-200 dark:divide-gray-700">
          {filteredNetworks.length > 0 ? (
            filteredNetworks.map((network) => (
              <NetworkCard
                key={network.id}
                network={network}
                isSelected={selectedItems.includes(network.id)}
                onToggle={onNetworkToggle}
                onAction={onItemAction}
                onSelectionChange={handleNetworkSelection}
              />
            ))
          ) : (
            <div className="px-6 py-12 text-center">
              <Server className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                {t('networks.noNetworks')}
              </h3>
              <p className="text-gray-500 dark:text-gray-400">
                {searchTerm || selectedFilter !== 'all' 
                  ? t('networks.noNetworksFiltered')
                  : t('networks.noNetworksFound')
                }
              </p>
            </div>
          )}
        </div>
      </div>

      {/* معلومات إضافية */}
      <div className="text-sm text-gray-500 dark:text-gray-400 text-center">
        {t('networks.lastUpdated')}: {new Date().toLocaleString()}
      </div>
    </div>
  );
}
