<?php
/**
 * Token Management API for Enhanced Smart Contracts
 * API إدارة العملات للعقود الذكية المحسنة
 * 
 * يوفر وظائف إدارة العملات (إضافة، تعديل، حذف، تحقق)
 */

// إعدادات CORS
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// تضمين الملفات المطلوبة
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../middleware/admin_auth.php';

/**
 * فئة إدارة العملات
 */
class TokenManagementAPI {
    private $db;
    
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * معالجة الطلبات
     */
    public function handleRequest() {
        // التحقق من صلاحيات الإدارة
        $adminAuth = checkAdminAuth();
        if (!$adminAuth['success']) {
            $this->sendError('Unauthorized access', 401);
            return;
        }
        
        $method = $_SERVER['REQUEST_METHOD'];
        $action = $_GET['action'] ?? '';
        
        try {
            switch ($method) {
                case 'GET':
                    $this->handleGet($action);
                    break;
                case 'POST':
                    $this->handlePost($action);
                    break;
                case 'PUT':
                    $this->handlePut($action);
                    break;
                case 'DELETE':
                    $this->handleDelete($action);
                    break;
                default:
                    $this->sendError('Method not allowed', 405);
            }
        } catch (Exception $e) {
            $this->sendError($e->getMessage(), 500);
        }
    }
    
    /**
     * معالجة طلبات GET
     */
    private function handleGet($action) {
        switch ($action) {
            case 'list':
                $networkId = intval($_GET['network_id'] ?? 0);
                $this->getTokensList($networkId);
                break;
            case 'details':
                $tokenId = intval($_GET['id'] ?? 0);
                $this->getTokenDetails($tokenId);
                break;
            case 'validate-contract':
                $contractAddress = $_GET['contract_address'] ?? '';
                $networkId = intval($_GET['network_id'] ?? 0);
                $this->validateTokenContract($contractAddress, $networkId);
                break;
            case 'fetch-token-info':
                $contractAddress = $_GET['contract_address'] ?? '';
                $networkId = intval($_GET['network_id'] ?? 0);
                $this->fetchTokenInfoFromBlockchain($contractAddress, $networkId);
                break;
            default:
                $this->sendError('Invalid action', 400);
        }
    }
    
    /**
     * معالجة طلبات POST
     */
    private function handlePost($action) {
        $input = json_decode(file_get_contents('php://input'), true);
        
        switch ($action) {
            case 'add':
                $this->addToken($input);
                break;
            case 'import-from-contract':
                $this->importTokenFromContract($input);
                break;
            case 'bulk-import':
                $this->bulkImportTokens($input);
                break;
            default:
                $this->sendError('Invalid action', 400);
        }
    }
    
    /**
     * معالجة طلبات PUT
     */
    private function handlePut($action) {
        $input = json_decode(file_get_contents('php://input'), true);
        
        switch ($action) {
            case 'update':
                $tokenId = intval($_GET['id'] ?? 0);
                $this->updateToken($tokenId, $input);
                break;
            case 'toggle-status':
                $tokenId = intval($_GET['id'] ?? 0);
                $this->toggleTokenStatus($tokenId);
                break;
            case 'update-prices':
                $this->updateTokenPrices();
                break;
            default:
                $this->sendError('Invalid action', 400);
        }
    }
    
    /**
     * معالجة طلبات DELETE
     */
    private function handleDelete($action) {
        switch ($action) {
            case 'remove':
                $tokenId = intval($_GET['id'] ?? 0);
                $this->removeToken($tokenId);
                break;
            case 'bulk-remove':
                $input = json_decode(file_get_contents('php://input'), true);
                $this->bulkRemoveTokens($input);
                break;
            default:
                $this->sendError('Invalid action', 400);
        }
    }
    
    /**
     * الحصول على قائمة العملات
     */
    private function getTokensList($networkId = 0) {
        $pdo = $this->db->getConnection();
        
        $whereClause = '';
        $params = [];
        
        if ($networkId > 0) {
            $whereClause = 'WHERE t.network_id = ?';
            $params[] = $networkId;
        }
        
        $stmt = $pdo->prepare("
            SELECT 
                t.*,
                n.network_name,
                n.network_symbol,
                n.chain_id,
                n.explorer_url,
                n.is_testnet as network_is_testnet,
                COUNT(DISTINCT o.id) as offers_count,
                COALESCE(SUM(o.amount), 0) as total_volume
            FROM supported_tokens t
            LEFT JOIN supported_networks n ON t.network_id = n.id
            LEFT JOIN offers o ON t.id = o.token_id AND o.is_active = 1
            $whereClause
            GROUP BY t.id
            ORDER BY t.is_stablecoin DESC, t.is_active DESC, t.token_symbol ASC
        ");
        
        $stmt->execute($params);
        $tokens = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // تحويل البيانات للتنسيق المطلوب
        foreach ($tokens as &$token) {
            $token['id'] = (int)$token['id'];
            $token['network_id'] = (int)$token['network_id'];
            $token['decimals'] = (int)$token['decimals'];
            $token['is_stablecoin'] = (bool)$token['is_stablecoin'];
            $token['is_active'] = (bool)$token['is_active'];
            $token['min_trade_amount'] = (float)$token['min_trade_amount'];
            $token['max_trade_amount'] = (float)$token['max_trade_amount'];
            $token['daily_volume_limit'] = (float)$token['daily_volume_limit'];
            $token['platform_fee_rate'] = (float)$token['platform_fee_rate'];
            $token['offers_count'] = (int)$token['offers_count'];
            $token['total_volume'] = (float)$token['total_volume'];
            $token['network_is_testnet'] = (bool)$token['network_is_testnet'];
        }
        
        $this->sendSuccess([
            'tokens' => $tokens,
            'total' => count($tokens),
            'active_count' => count(array_filter($tokens, function($t) { return $t['is_active']; })),
            'stablecoin_count' => count(array_filter($tokens, function($t) { return $t['is_stablecoin']; })),
            'network_filter' => $networkId
        ]);
    }
    
    /**
     * الحصول على تفاصيل عملة محددة
     */
    private function getTokenDetails($tokenId) {
        if ($tokenId <= 0) {
            $this->sendError('Invalid token ID', 400);
            return;
        }
        
        $pdo = $this->db->getConnection();
        
        // جلب بيانات العملة
        $stmt = $pdo->prepare("
            SELECT 
                t.*,
                n.network_name,
                n.network_symbol,
                n.chain_id,
                n.explorer_url,
                n.rpc_url,
                n.is_testnet as network_is_testnet
            FROM supported_tokens t
            LEFT JOIN supported_networks n ON t.network_id = n.id
            WHERE t.id = ?
        ");
        $stmt->execute([$tokenId]);
        $token = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$token) {
            $this->sendError('Token not found', 404);
            return;
        }
        
        // جلب إحصائيات العملة
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(DISTINCT o.id) as total_offers,
                COUNT(DISTINCT CASE WHEN o.is_active = 1 THEN o.id END) as active_offers,
                COUNT(DISTINCT CASE WHEN o.offer_type = 'sell' THEN o.id END) as sell_offers,
                COUNT(DISTINCT CASE WHEN o.offer_type = 'buy' THEN o.id END) as buy_offers,
                COALESCE(SUM(CASE WHEN o.contract_status = 'completed' THEN o.amount END), 0) as total_volume,
                COALESCE(AVG(CASE WHEN o.contract_status = 'completed' THEN o.price END), 0) as avg_price
            FROM offers o
            WHERE o.token_id = ?
        ");
        $stmt->execute([$tokenId]);
        $stats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // جلب آخر العروض
        $stmt = $pdo->prepare("
            SELECT 
                o.*,
                u.username as user_username
            FROM offers o
            LEFT JOIN users u ON o.user_id = u.id
            WHERE o.token_id = ?
            ORDER BY o.created_at DESC
            LIMIT 10
        ");
        $stmt->execute([$tokenId]);
        $recentOffers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $this->sendSuccess([
            'token' => $token,
            'stats' => $stats,
            'recent_offers' => $recentOffers
        ]);
    }
    
    /**
     * التحقق من عقد العملة
     */
    private function validateTokenContract($contractAddress, $networkId) {
        if (empty($contractAddress) || $networkId <= 0) {
            $this->sendError('Contract address and network ID are required', 400);
            return;
        }

        if (!$this->isValidAddress($contractAddress)) {
            $this->sendError('Invalid contract address format', 400);
            return;
        }

        try {
            // جلب معلومات الشبكة
            $pdo = $this->db->getConnection();
            $stmt = $pdo->prepare("SELECT * FROM supported_networks WHERE id = ? AND is_active = 1");
            $stmt->execute([$networkId]);
            $network = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$network) {
                $this->sendError('Network not found or inactive', 404);
                return;
            }

            // التحقق من وجود العقد على البلوك تشين
            $contractExists = $this->checkContractExists($contractAddress, $network['rpc_url']);

            if (!$contractExists) {
                $this->sendError('Contract not found on blockchain', 404);
                return;
            }

            // جلب معلومات العملة من العقد
            $tokenInfo = $this->fetchTokenInfoFromContract($contractAddress, $network['rpc_url']);

            $this->sendSuccess([
                'valid' => true,
                'contract_address' => $contractAddress,
                'network_id' => $networkId,
                'network_name' => $network['network_name'],
                'token_info' => $tokenInfo,
                'is_stablecoin' => $this->isStablecoin($tokenInfo['symbol'] ?? '', $tokenInfo['name'] ?? '')
            ]);

        } catch (Exception $e) {
            $this->sendError('Contract validation failed: ' . $e->getMessage(), 400);
        }
    }

    /**
     * جلب معلومات العملة من البلوك تشين
     */
    private function fetchTokenInfoFromBlockchain($contractAddress, $networkId) {
        if (empty($contractAddress) || $networkId <= 0) {
            $this->sendError('Contract address and network ID are required', 400);
            return;
        }

        try {
            // جلب معلومات الشبكة
            $pdo = $this->db->getConnection();
            $stmt = $pdo->prepare("SELECT * FROM supported_networks WHERE id = ? AND is_active = 1");
            $stmt->execute([$networkId]);
            $network = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$network) {
                $this->sendError('Network not found or inactive', 404);
                return;
            }

            // جلب معلومات العملة
            $tokenInfo = $this->fetchTokenInfoFromContract($contractAddress, $network['rpc_url']);

            $this->sendSuccess([
                'contract_address' => $contractAddress,
                'network_id' => $networkId,
                'network_name' => $network['network_name'],
                'chain_id' => $network['chain_id'],
                'token_info' => $tokenInfo,
                'suggested_settings' => [
                    'is_stablecoin' => $this->isStablecoin($tokenInfo['symbol'] ?? '', $tokenInfo['name'] ?? ''),
                    'min_trade_amount' => $this->isStablecoin($tokenInfo['symbol'] ?? '', $tokenInfo['name'] ?? '') ? 1.0 : 0.001,
                    'max_trade_amount' => $this->isStablecoin($tokenInfo['symbol'] ?? '', $tokenInfo['name'] ?? '') ? 100000.0 : 1000.0,
                    'platform_fee_rate' => 0.005
                ]
            ]);

        } catch (Exception $e) {
            $this->sendError('Failed to fetch token info: ' . $e->getMessage(), 400);
        }
    }

    /**
     * إضافة عملة جديدة
     */
    private function addToken($input) {
        // التحقق من البيانات المطلوبة
        $required = ['network_id', 'token_address', 'token_symbol', 'token_name', 'decimals'];
        foreach ($required as $field) {
            if (!isset($input[$field]) || $input[$field] === '') {
                $this->sendError("Field '$field' is required", 400);
                return;
            }
        }

        if (!$this->isValidAddress($input['token_address'])) {
            $this->sendError('Invalid token address format', 400);
            return;
        }

        $pdo = $this->db->getConnection();

        try {
            // التحقق من عدم تكرار العنوان في نفس الشبكة
            $stmt = $pdo->prepare("
                SELECT id FROM supported_tokens
                WHERE network_id = ? AND token_address = ?
            ");
            $stmt->execute([$input['network_id'], $input['token_address']]);
            if ($stmt->fetch()) {
                $this->sendError('Token already exists on this network', 409);
                return;
            }

            // التحقق من وجود الشبكة
            $stmt = $pdo->prepare("SELECT * FROM supported_networks WHERE id = ? AND is_active = 1");
            $stmt->execute([$input['network_id']]);
            $network = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$network) {
                $this->sendError('Network not found or inactive', 404);
                return;
            }

            // التحقق من العقد على البلوك تشين (اختياري)
            if ($input['validate_contract'] ?? true) {
                $contractExists = $this->checkContractExists($input['token_address'], $network['rpc_url']);
                if (!$contractExists) {
                    $this->sendError('Contract not found on blockchain', 404);
                    return;
                }
            }

            // إدراج العملة الجديدة
            $stmt = $pdo->prepare("
                INSERT INTO supported_tokens (
                    network_id, token_address, token_symbol, token_name, decimals,
                    is_stablecoin, is_active, coingecko_id, icon_url,
                    min_trade_amount, max_trade_amount, daily_volume_limit, platform_fee_rate,
                    created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
            ");

            $stmt->execute([
                $input['network_id'],
                $input['token_address'],
                $input['token_symbol'],
                $input['token_name'],
                $input['decimals'],
                $input['is_stablecoin'] ?? false,
                $input['is_active'] ?? true,
                $input['coingecko_id'] ?? null,
                $input['icon_url'] ?? null,
                $input['min_trade_amount'] ?? 1.0,
                $input['max_trade_amount'] ?? 100000.0,
                $input['daily_volume_limit'] ?? 50000.0,
                $input['platform_fee_rate'] ?? 0.005
            ]);

            $tokenId = $pdo->lastInsertId();

            // تسجيل العملية في سجل التحديثات
            $this->logTokenUpdate('token_added', $input['network_id'], $tokenId, null, $input);

            $this->sendSuccess([
                'token_id' => $tokenId,
                'message' => 'Token added successfully',
                'network_name' => $network['network_name']
            ]);

        } catch (Exception $e) {
            $this->sendError('Failed to add token: ' . $e->getMessage(), 500);
        }
    }

    /**
     * إرسال استجابة نجاح
     */
    private function sendSuccess($data, $statusCode = 200) {
        http_response_code($statusCode);
        echo json_encode([
            'success' => true,
            'data' => $data,
            'timestamp' => date('Y-m-d H:i:s')
        ], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        exit();
    }
    
    /**
     * التحقق من وجود العقد على البلوك تشين
     */
    private function checkContractExists($contractAddress, $rpcUrl) {
        $context = stream_context_create([
            'http' => [
                'method' => 'POST',
                'header' => 'Content-Type: application/json',
                'content' => json_encode([
                    'jsonrpc' => '2.0',
                    'method' => 'eth_getCode',
                    'params' => [$contractAddress, 'latest'],
                    'id' => 1
                ]),
                'timeout' => 10
            ]
        ]);

        $response = file_get_contents($rpcUrl, false, $context);

        if ($response === false) {
            throw new Exception('Failed to connect to RPC endpoint');
        }

        $data = json_decode($response, true);

        if (!$data || isset($data['error'])) {
            throw new Exception('RPC returned error: ' . ($data['error']['message'] ?? 'Unknown error'));
        }

        // إذا كان الكود أكثر من 0x فهذا يعني أن العقد موجود
        return $data['result'] !== '0x';
    }

    /**
     * جلب معلومات العملة من العقد
     */
    private function fetchTokenInfoFromContract($contractAddress, $rpcUrl) {
        $tokenInfo = [];

        // جلب اسم العملة
        try {
            $name = $this->callContractFunction($contractAddress, '0x06fdde03', $rpcUrl); // name()
            $tokenInfo['name'] = $this->hexToString($name);
        } catch (Exception $e) {
            $tokenInfo['name'] = 'Unknown Token';
        }

        // جلب رمز العملة
        try {
            $symbol = $this->callContractFunction($contractAddress, '0x95d89b41', $rpcUrl); // symbol()
            $tokenInfo['symbol'] = $this->hexToString($symbol);
        } catch (Exception $e) {
            $tokenInfo['symbol'] = 'UNKNOWN';
        }

        // جلب عدد الخانات العشرية
        try {
            $decimals = $this->callContractFunction($contractAddress, '0x313ce567', $rpcUrl); // decimals()
            $tokenInfo['decimals'] = hexdec($decimals);
        } catch (Exception $e) {
            $tokenInfo['decimals'] = 18;
        }

        // جلب إجمالي المعروض
        try {
            $totalSupply = $this->callContractFunction($contractAddress, '0x18160ddd', $rpcUrl); // totalSupply()
            $tokenInfo['total_supply'] = $totalSupply;
        } catch (Exception $e) {
            $tokenInfo['total_supply'] = '0x0';
        }

        return $tokenInfo;
    }

    /**
     * استدعاء دالة في العقد الذكي
     */
    private function callContractFunction($contractAddress, $data, $rpcUrl) {
        $context = stream_context_create([
            'http' => [
                'method' => 'POST',
                'header' => 'Content-Type: application/json',
                'content' => json_encode([
                    'jsonrpc' => '2.0',
                    'method' => 'eth_call',
                    'params' => [
                        [
                            'to' => $contractAddress,
                            'data' => $data
                        ],
                        'latest'
                    ],
                    'id' => 1
                ]),
                'timeout' => 10
            ]
        ]);

        $response = file_get_contents($rpcUrl, false, $context);

        if ($response === false) {
            throw new Exception('Failed to connect to RPC endpoint');
        }

        $responseData = json_decode($response, true);

        if (!$responseData || isset($responseData['error'])) {
            throw new Exception('RPC returned error: ' . ($responseData['error']['message'] ?? 'Unknown error'));
        }

        return $responseData['result'];
    }

    /**
     * تحويل Hex إلى نص
     */
    private function hexToString($hex) {
        $hex = str_replace('0x', '', $hex);
        $string = '';
        for ($i = 0; $i < strlen($hex) - 1; $i += 2) {
            $char = chr(hexdec($hex[$i] . $hex[$i + 1]));
            if ($char !== "\0") {
                $string .= $char;
            }
        }
        return trim($string);
    }

    /**
     * فحص إذا كانت العملة مستقرة
     */
    private function isStablecoin($symbol, $name = '') {
        $symbol = strtoupper($symbol);
        $name = strtoupper($name);

        $stablecoins = ['USDT', 'USDC', 'BUSD', 'DAI', 'TUSD', 'USDD', 'FRAX', 'LUSD', 'FDUSD'];

        foreach ($stablecoins as $stable) {
            if (strpos($symbol, $stable) !== false || strpos($name, $stable) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * التحقق من صحة عنوان العقد
     */
    private function isValidAddress($address) {
        return preg_match('/^0x[a-fA-F0-9]{40}$/', $address);
    }

    /**
     * تسجيل تحديث العملة في السجل
     */
    private function logTokenUpdate($updateType, $networkId, $tokenId, $oldData, $newData) {
        try {
            $pdo = $this->db->getConnection();

            $stmt = $pdo->prepare("
                INSERT INTO network_token_updates (
                    update_type, network_id, token_id, admin_id, old_data, new_data,
                    update_source, notes, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
            ");

            $stmt->execute([
                $updateType,
                $networkId,
                $tokenId,
                1, // TODO: الحصول على معرف المدير الحقيقي من الجلسة
                $oldData ? json_encode($oldData) : null,
                $newData ? json_encode($newData) : null,
                'manual',
                "Token $updateType operation"
            ]);

        } catch (Exception $e) {
            // تسجيل الخطأ لكن لا نوقف العملية
            error_log("Failed to log token update: " . $e->getMessage());
        }
    }

    /**
     * إرسال استجابة خطأ
     */
    private function sendError($message, $statusCode = 400) {
        http_response_code($statusCode);
        echo json_encode([
            'success' => false,
            'error' => $message,
            'timestamp' => date('Y-m-d H:i:s')
        ], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        exit();
    }
}

// تشغيل API
try {
    $api = new TokenManagementAPI();
    $api->handleRequest();
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Internal server error: ' . $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
}
?>
