<?php
/**
 * Contract Verification API for Enhanced Smart Contracts
 * API التحقق من العقود الذكية للعقود المحسنة
 * 
 * يوفر وظائف التحقق من العقود الذكية وتحليلها وجلب البيانات من البلوك تشين
 */

// إعدادات CORS
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// تضمين الملفات المطلوبة
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../middleware/admin_auth.php';

/**
 * فئة التحقق من العقود الذكية
 */
class ContractVerificationAPI {
    private $db;
    private $supportedNetworks;
    
    public function __construct() {
        $this->db = new Database();
        $this->initializeSupportedNetworks();
    }
    
    /**
     * تهيئة الشبكات المدعومة من قاعدة البيانات
     */
    private function initializeSupportedNetworks() {
        try {
            $pdo = $this->db->getConnection();
            $stmt = $pdo->prepare("
                SELECT
                    chain_id, network_name, network_symbol, rpc_url, explorer_url,
                    is_testnet, is_active, gas_price_gwei, block_time_seconds, confirmation_blocks
                FROM supported_networks
                WHERE is_active = TRUE
                ORDER BY is_testnet ASC, chain_id ASC
            ");
            $stmt->execute();
            $networks = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $this->supportedNetworks = [];
            foreach ($networks as $network) {
                // تحديد API URL بناءً على الشبكة
                $apiUrl = $this->getApiUrlForNetwork($network['chain_id']);
                $apiKeyEnv = $this->getApiKeyEnvForNetwork($network['chain_id']);

                $this->supportedNetworks[$network['chain_id']] = [
                    'name' => $network['network_name'],
                    'chain_id' => $network['chain_id'],
                    'api_url' => $apiUrl,
                    'api_key_env' => $apiKeyEnv,
                    'explorer_url' => $network['explorer_url'],
                    'native_currency' => $network['network_symbol'],
                    'is_testnet' => (bool)$network['is_testnet'],
                    'is_active' => (bool)$network['is_active'],
                    'rpc_url' => $network['rpc_url'],
                    'gas_price_gwei' => $network['gas_price_gwei'],
                    'block_time_seconds' => $network['block_time_seconds'],
                    'confirmation_blocks' => $network['confirmation_blocks']
                ];
            }

        } catch (Exception $e) {
            // Fallback إلى الشبكات الثابتة في حالة الخطأ
            $this->supportedNetworks = [
                97 => [
                    'name' => 'BSC Testnet',
                    'chain_id' => 97,
                    'api_url' => 'https://api-testnet.bscscan.com/api',
                    'api_key_env' => 'BSCSCAN_API_KEY',
                    'explorer_url' => 'https://testnet.bscscan.com',
                    'native_currency' => 'tBNB',
                    'is_testnet' => true,
                    'is_active' => true,
                    'rpc_url' => 'https://data-seed-prebsc-1-s1.binance.org:8545/',
                    'gas_price_gwei' => 10.0,
                    'block_time_seconds' => 3,
                    'confirmation_blocks' => 3
                ]
            ];
        }
    }

    /**
     * الحصول على API URL للشبكة
     */
    private function getApiUrlForNetwork($chainId) {
        $apiUrls = [
            1 => 'https://api.etherscan.io/api',
            56 => 'https://api.bscscan.com/api',
            97 => 'https://api-testnet.bscscan.com/api',
            137 => 'https://api.polygonscan.com/api',
            80001 => 'https://api-testnet.polygonscan.com/api',
            42161 => 'https://api.arbiscan.io/api',
            421614 => 'https://api-sepolia.arbiscan.io/api',
            10 => 'https://api-optimistic.etherscan.io/api',
            11155420 => 'https://api-sepolia-optimistic.etherscan.io/api',
            43114 => 'https://api.snowtrace.io/api',
            43113 => 'https://api-testnet.snowtrace.io/api'
        ];

        return $apiUrls[$chainId] ?? '';
    }

    /**
     * الحصول على متغير بيئة مفتاح API للشبكة
     */
    private function getApiKeyEnvForNetwork($chainId) {
        $apiKeyEnvs = [
            1 => 'ETHERSCAN_API_KEY',
            11155111 => 'ETHERSCAN_API_KEY',
            56 => 'BSCSCAN_API_KEY',
            97 => 'BSCSCAN_API_KEY',
            137 => 'POLYGONSCAN_API_KEY',
            80001 => 'POLYGONSCAN_API_KEY',
            42161 => 'ARBISCAN_API_KEY',
            421614 => 'ARBISCAN_API_KEY',
            10 => 'OPTIMISM_API_KEY',
            11155420 => 'OPTIMISM_API_KEY',
            43114 => 'SNOWTRACE_API_KEY',
            43113 => 'SNOWTRACE_API_KEY'
        ];

        return $apiKeyEnvs[$chainId] ?? 'ETHERSCAN_API_KEY';
    }
    
    /**
     * معالجة الطلبات
     */
    public function handleRequest() {
        // التحقق من صلاحيات الإدارة
        $adminAuth = checkAdminAuth();
        if (!$adminAuth['success']) {
            $this->sendError('Unauthorized access', 401);
            return;
        }
        
        $method = $_SERVER['REQUEST_METHOD'];
        $action = $_GET['action'] ?? '';
        
        try {
            switch ($method) {
                case 'GET':
                    $this->handleGet($action);
                    break;
                case 'POST':
                    $this->handlePost($action);
                    break;
                default:
                    $this->sendError('Method not allowed', 405);
            }
        } catch (Exception $e) {
            $this->sendError($e->getMessage(), 500);
        }
    }
    
    /**
     * معالجة طلبات GET
     */
    private function handleGet($action) {
        switch ($action) {
            case 'supported-networks':
                $this->getSupportedNetworks();
                break;
            case 'contract-info':
                $contractAddress = $_GET['address'] ?? '';
                $chainId = intval($_GET['chain_id'] ?? 97);
                $this->getContractInfo($contractAddress, $chainId);
                break;
            case 'token-info':
                $contractAddress = $_GET['address'] ?? '';
                $chainId = intval($_GET['chain_id'] ?? 97);
                $this->getTokenInfo($contractAddress, $chainId);
                break;
            default:
                $this->sendError('Invalid action', 400);
        }
    }
    
    /**
     * معالجة طلبات POST
     */
    private function handlePost($action) {
        $input = json_decode(file_get_contents('php://input'), true);
        
        switch ($action) {
            case 'verify-contract':
                $this->verifyContract($input);
                break;
            case 'analyze-contract':
                $this->analyzeContract($input);
                break;
            case 'auto-populate':
                $this->autoPopulateContractData($input);
                break;
            default:
                $this->sendError('Invalid action', 400);
        }
    }
    
    /**
     * الحصول على الشبكات المدعومة
     */
    private function getSupportedNetworks() {
        try {
            $pdo = $this->db->getConnection();

            // جلب الشبكات من قاعدة البيانات
            $stmt = $pdo->prepare("
                SELECT
                    chain_id,
                    network_name as name,
                    network_symbol as native_currency,
                    explorer_url,
                    is_testnet,
                    is_active
                FROM supported_networks
                WHERE is_active = 1
                ORDER BY is_testnet ASC, network_name ASC
            ");

            $stmt->execute();
            $dbNetworks = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $networks = [];

            // إذا لم توجد شبكات في قاعدة البيانات، استخدم البيانات الافتراضية
            if (empty($dbNetworks)) {
                foreach ($this->supportedNetworks as $chainId => $network) {
                    $networks[] = [
                        'chain_id' => $chainId,
                        'name' => $network['name'],
                        'explorer_url' => $network['explorer_url'],
                        'native_currency' => $network['native_currency'],
                        'is_testnet' => strpos(strtolower($network['name']), 'test') !== false,
                        'is_active' => true
                    ];
                }
            } else {
                // استخدم البيانات من قاعدة البيانات
                foreach ($dbNetworks as $network) {
                    $networks[] = [
                        'chain_id' => (int)$network['chain_id'],
                        'name' => $network['name'],
                        'explorer_url' => $network['explorer_url'],
                        'native_currency' => $network['native_currency'],
                        'is_testnet' => (bool)$network['is_testnet'],
                        'is_active' => (bool)$network['is_active']
                    ];
                }
            }

            $this->sendSuccess([
                'networks' => $networks,
                'total' => count($networks),
                'source' => empty($dbNetworks) ? 'default' : 'database'
            ]);

        } catch (Exception $e) {
            // في حالة خطأ قاعدة البيانات، استخدم البيانات الافتراضية
            $networks = [];
            foreach ($this->supportedNetworks as $chainId => $network) {
                $networks[] = [
                    'chain_id' => $chainId,
                    'name' => $network['name'],
                    'explorer_url' => $network['explorer_url'],
                    'native_currency' => $network['native_currency'],
                    'is_testnet' => strpos(strtolower($network['name']), 'test') !== false,
                    'is_active' => true
                ];
            }

            $this->sendSuccess([
                'networks' => $networks,
                'total' => count($networks),
                'source' => 'fallback',
                'warning' => 'Database connection failed, using default networks'
            ]);
        }
    }
    
    /**
     * التحقق من العقد الذكي
     */
    private function verifyContract($input) {
        $contractAddress = $input['contract_address'] ?? '';
        $chainId = intval($input['chain_id'] ?? 97);

        if (!$this->isValidAddress($contractAddress)) {
            $this->sendError('Invalid contract address', 400);
            return;
        }

        if (!isset($this->supportedNetworks[$chainId])) {
            $this->sendError('Unsupported network', 400);
            return;
        }

        try {
            // التحقق من وجود العقد في قاعدة البيانات أولاً
            $existingContract = $this->checkContractInDatabase($contractAddress, $chainId);

            if ($existingContract) {
                // العقد موجود في قاعدة البيانات، استخدم البيانات المحفوظة
                $this->sendSuccess([
                    'verified' => true,
                    'contract_info' => [
                        'address' => $existingContract['contract_address'],
                        'name' => $existingContract['contract_type'],
                        'version' => $existingContract['contract_version'],
                        'is_verified' => true,
                        'source' => 'database'
                    ],
                    'contract_type' => [
                        'is_escrow' => strpos(strtolower($existingContract['contract_type']), 'escrow') !== false,
                        'is_reputation' => strpos(strtolower($existingContract['contract_type']), 'reputation') !== false,
                        'is_oracle' => strpos(strtolower($existingContract['contract_type']), 'oracle') !== false,
                        'is_admin' => strpos(strtolower($existingContract['contract_type']), 'admin') !== false,
                        'contract_type' => $existingContract['contract_type']
                    ],
                    'network_info' => $this->supportedNetworks[$chainId],
                    'database_info' => $existingContract
                ]);
                return;
            }

            // التحقق من وجود العقد على البلوك تشين
            $contractExists = $this->checkContractExists($contractAddress, $chainId);
            if (!$contractExists) {
                $this->sendError('Contract not found on blockchain', 404);
                return;
            }

            // جلب معلومات العقد من البلوك تشين
            $contractInfo = $this->fetchContractInfo($contractAddress, $chainId);

            // تحليل نوع العقد
            $contractType = $this->analyzeContractType($contractInfo);

            // جلب العملات المدعومة إذا كان عقد token
            $supportedTokens = [];
            if ($contractType['is_token']) {
                $supportedTokens = $this->fetchTokenInfo($contractAddress, $chainId);
            }

            $this->sendSuccess([
                'verified' => true,
                'contract_info' => $contractInfo,
                'contract_type' => $contractType,
                'supported_tokens' => $supportedTokens,
                'network_info' => $this->supportedNetworks[$chainId],
                'source' => 'blockchain'
            ]);

        } catch (Exception $e) {
            $this->sendError('Verification failed: ' . $e->getMessage(), 500);
        }
    }

    /**
     * التحقق من وجود العقد في قاعدة البيانات
     */
    private function checkContractInDatabase($contractAddress, $chainId) {
        try {
            $pdo = $this->db->getConnection();

            // البحث عن العقد في جدول العقود المحسنة
            $stmt = $pdo->prepare("
                SELECT ec.*, sn.network_name, sn.chain_id
                FROM enhanced_contracts ec
                JOIN supported_networks sn ON ec.network_id = sn.id
                WHERE ec.contract_address = ? AND sn.chain_id = ? AND ec.is_active = TRUE
            ");
            $stmt->execute([strtolower($contractAddress), $chainId]);
            $contract = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($contract) {
                return $contract;
            }

            return null;

        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * تحليل العقد الذكي بشكل شامل
     */
    private function analyzeContract($input) {
        $contractAddress = $input['contract_address'] ?? '';
        $chainId = intval($input['chain_id'] ?? 97);

        if (!$this->isValidAddress($contractAddress)) {
            $this->sendError('Invalid contract address', 400);
            return;
        }

        try {
            // التحقق من وجود العقد
            $contractExists = $this->checkContractExists($contractAddress, $chainId);
            if (!$contractExists) {
                $this->sendError('Contract not found on blockchain', 404);
                return;
            }

            // جلب معلومات العقد
            $contractInfo = $this->fetchContractInfo($contractAddress, $chainId);

            // تحليل نوع العقد
            $contractType = $this->analyzeContractType($contractInfo);

            // تحليل الأمان
            $securityAnalysis = $this->analyzeContractSecurity($contractInfo);

            // جلب إحصائيات العقد
            $contractStats = $this->getContractStatistics($contractAddress, $chainId);

            // تحديد العملات المدعومة
            $supportedTokens = [];
            if ($contractType['is_token']) {
                $tokenInfo = $this->fetchTokenInfo($contractAddress, $chainId);
                $supportedTokens[] = array_merge($tokenInfo, [
                    'contract_address' => $contractAddress,
                    'is_stablecoin' => $this->isStablecoin($tokenInfo),
                    'is_verified' => !empty($contractInfo['source_code'])
                ]);
            } elseif ($contractType['is_escrow'] || $contractType['is_defi']) {
                // للعقود المعقدة، نحاول جلب العملات المدعومة من الأحداث
                $supportedTokens = $this->extractSupportedTokensFromEvents($contractAddress, $chainId);
            }

            $this->sendSuccess([
                'contract_address' => $contractAddress,
                'chain_id' => $chainId,
                'network_info' => $this->supportedNetworks[$chainId],
                'contract_info' => $contractInfo,
                'contract_type' => $contractType,
                'security_analysis' => $securityAnalysis,
                'contract_stats' => $contractStats,
                'supported_tokens' => $supportedTokens,
                'recommendations' => $this->generateRecommendations($contractType, $securityAnalysis)
            ]);

        } catch (Exception $e) {
            $this->sendError('Analysis failed: ' . $e->getMessage(), 500);
        }
    }

    /**
     * ملء البيانات تلقائياً وحفظها في قاعدة البيانات
     */
    private function autoPopulateContractData($input) {
        $contractAddress = $input['contract_address'] ?? '';
        $chainId = intval($input['chain_id'] ?? 97);
        $saveToDatabase = $input['save_to_database'] ?? true;

        if (!$this->isValidAddress($contractAddress)) {
            $this->sendError('Invalid contract address', 400);
            return;
        }

        try {
            // تحليل شامل للعقد
            $analysisResult = $this->performFullAnalysis($contractAddress, $chainId);

            if ($saveToDatabase) {
                // حفظ البيانات في قاعدة البيانات
                $savedData = $this->saveContractToDatabase($analysisResult);

                $this->sendSuccess([
                    'message' => 'Contract data populated and saved successfully',
                    'analysis' => $analysisResult,
                    'saved_data' => $savedData
                ]);
            } else {
                $this->sendSuccess([
                    'message' => 'Contract data populated successfully',
                    'analysis' => $analysisResult
                ]);
            }

        } catch (Exception $e) {
            $this->sendError('Auto-population failed: ' . $e->getMessage(), 500);
        }
    }

    /**
     * تحليل شامل للعقد
     */
    private function performFullAnalysis($contractAddress, $chainId) {
        // جلب معلومات العقد
        $contractInfo = $this->fetchContractInfo($contractAddress, $chainId);

        // تحليل نوع العقد
        $contractType = $this->analyzeContractType($contractInfo);

        // تحليل الأمان
        $securityAnalysis = $this->analyzeContractSecurity($contractInfo);

        // جلب إحصائيات العقد
        $contractStats = $this->getContractStatistics($contractAddress, $chainId);

        // تحديد العملات المدعومة
        $supportedTokens = [];
        if ($contractType['is_token']) {
            $tokenInfo = $this->fetchTokenInfo($contractAddress, $chainId);
            $supportedTokens[] = array_merge($tokenInfo, [
                'contract_address' => $contractAddress,
                'is_stablecoin' => $this->isStablecoin($tokenInfo),
                'is_verified' => !empty($contractInfo['source_code'])
            ]);
        }

        return [
            'contract_address' => $contractAddress,
            'chain_id' => $chainId,
            'network_info' => $this->supportedNetworks[$chainId],
            'contract_info' => $contractInfo,
            'contract_type' => $contractType,
            'security_analysis' => $securityAnalysis,
            'contract_stats' => $contractStats,
            'supported_tokens' => $supportedTokens
        ];
    }

    /**
     * حفظ بيانات العقد في قاعدة البيانات
     */
    private function saveContractToDatabase($analysisResult) {
        $pdo = $this->db->getConnection();
        $savedData = [];

        try {
            $pdo->beginTransaction();

            // التحقق من وجود الشبكة في قاعدة البيانات
            $networkId = $this->ensureNetworkExists($analysisResult['chain_id'], $analysisResult['network_info']);

            // حفظ العقد في جدول enhanced_contracts
            $contractId = $this->saveEnhancedContract($networkId, $analysisResult);
            $savedData['contract_id'] = $contractId;

            // حفظ العملات المدعومة
            if (!empty($analysisResult['supported_tokens'])) {
                $tokenIds = $this->saveSupportedTokens($networkId, $analysisResult['supported_tokens']);
                $savedData['token_ids'] = $tokenIds;
            }

            $pdo->commit();

            return $savedData;

        } catch (Exception $e) {
            $pdo->rollBack();
            throw new Exception('Failed to save contract data: ' . $e->getMessage());
        }
    }
    
    /**
     * إرسال استجابة نجاح
     */
    private function sendSuccess($data, $statusCode = 200) {
        http_response_code($statusCode);
        echo json_encode([
            'success' => true,
            'data' => $data,
            'timestamp' => date('Y-m-d H:i:s')
        ], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        exit();
    }
    
    /**
     * إرسال استجابة خطأ
     */
    private function sendError($message, $statusCode = 400) {
        http_response_code($statusCode);
        echo json_encode([
            'success' => false,
            'error' => $message,
            'timestamp' => date('Y-m-d H:i:s')
        ], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        exit();
    }
    
    /**
     * التحقق من وجود العقد على البلوك تشين
     */
    private function checkContractExists($contractAddress, $chainId) {
        $network = $this->supportedNetworks[$chainId];
        $apiKey = $_ENV[$network['api_key_env']] ?? 'YourApiKeyToken';

        $url = $network['api_url'] . '?' . http_build_query([
            'module' => 'proxy',
            'action' => 'eth_getCode',
            'address' => $contractAddress,
            'tag' => 'latest',
            'apikey' => $apiKey
        ]);

        $response = $this->makeApiRequest($url);

        if ($response && isset($response['result'])) {
            // إذا كان الكود أكثر من 0x فهذا يعني أن العقد موجود
            return $response['result'] !== '0x';
        }

        return false;
    }

    /**
     * جلب معلومات العقد من البلوك تشين
     */
    private function fetchContractInfo($contractAddress, $chainId) {
        $network = $this->supportedNetworks[$chainId];
        $apiKey = $_ENV[$network['api_key_env']] ?? 'YourApiKeyToken';

        $url = $network['api_url'] . '?' . http_build_query([
            'module' => 'contract',
            'action' => 'getsourcecode',
            'address' => $contractAddress,
            'apikey' => $apiKey
        ]);

        $response = $this->makeApiRequest($url);

        if ($response && isset($response['result'][0])) {
            $contractData = $response['result'][0];

            return [
                'address' => $contractAddress,
                'name' => $contractData['ContractName'] ?? 'Unknown',
                'source_code' => $contractData['SourceCode'] ?? '',
                'abi' => $contractData['ABI'] ?? '',
                'compiler_version' => $contractData['CompilerVersion'] ?? '',
                'optimization_used' => $contractData['OptimizationUsed'] ?? '',
                'runs' => $contractData['Runs'] ?? '',
                'constructor_arguments' => $contractData['ConstructorArguments'] ?? '',
                'evm_version' => $contractData['EVMVersion'] ?? '',
                'library' => $contractData['Library'] ?? '',
                'license_type' => $contractData['LicenseType'] ?? '',
                'proxy' => $contractData['Proxy'] ?? '',
                'implementation' => $contractData['Implementation'] ?? '',
                'swarm_source' => $contractData['SwarmSource'] ?? ''
            ];
        }

        throw new Exception('Failed to fetch contract information');
    }

    /**
     * جلب معلومات العملة (Token)
     */
    private function fetchTokenInfo($contractAddress, $chainId) {
        $network = $this->supportedNetworks[$chainId];
        $apiKey = $_ENV[$network['api_key_env']] ?? 'YourApiKeyToken';

        $tokenInfo = [];

        // جلب اسم العملة
        $nameData = $this->callContractFunction($contractAddress, '0x06fdde03', $chainId); // name()
        if ($nameData) {
            $tokenInfo['name'] = $this->hexToString($nameData);
        }

        // جلب رمز العملة
        $symbolData = $this->callContractFunction($contractAddress, '0x95d89b41', $chainId); // symbol()
        if ($symbolData) {
            $tokenInfo['symbol'] = $this->hexToString($symbolData);
        }

        // جلب عدد الخانات العشرية
        $decimalsData = $this->callContractFunction($contractAddress, '0x313ce567', $chainId); // decimals()
        if ($decimalsData) {
            $tokenInfo['decimals'] = hexdec($decimalsData);
        }

        // جلب إجمالي المعروض
        $totalSupplyData = $this->callContractFunction($contractAddress, '0x18160ddd', $chainId); // totalSupply()
        if ($totalSupplyData) {
            $tokenInfo['total_supply'] = $totalSupplyData;
        }

        return $tokenInfo;
    }

    /**
     * استدعاء دالة في العقد الذكي
     */
    private function callContractFunction($contractAddress, $data, $chainId) {
        $network = $this->supportedNetworks[$chainId];
        $apiKey = $_ENV[$network['api_key_env']] ?? 'YourApiKeyToken';

        $url = $network['api_url'] . '?' . http_build_query([
            'module' => 'proxy',
            'action' => 'eth_call',
            'to' => $contractAddress,
            'data' => $data,
            'tag' => 'latest',
            'apikey' => $apiKey
        ]);

        $response = $this->makeApiRequest($url);

        if ($response && isset($response['result'])) {
            return $response['result'];
        }

        return null;
    }

    /**
     * تحليل نوع العقد
     */
    private function analyzeContractType($contractInfo) {
        $sourceCode = strtolower($contractInfo['source_code'] ?? '');
        $contractName = strtolower($contractInfo['name'] ?? '');
        $abi = $contractInfo['abi'] ?? '';

        $analysis = [
            'is_token' => false,
            'is_nft' => false,
            'is_escrow' => false,
            'is_defi' => false,
            'is_proxy' => false,
            'contract_type' => 'unknown',
            'functions' => [],
            'events' => []
        ];

        // تحليل ABI للحصول على الوظائف والأحداث
        if (!empty($abi) && $abi !== 'Contract source code not verified') {
            $abiData = json_decode($abi, true);
            if (is_array($abiData)) {
                foreach ($abiData as $item) {
                    if ($item['type'] === 'function') {
                        $analysis['functions'][] = $item['name'];
                    } elseif ($item['type'] === 'event') {
                        $analysis['events'][] = $item['name'];
                    }
                }
            }
        }

        // تحديد نوع العقد بناءً على الوظائف
        $functions = $analysis['functions'];

        // فحص إذا كان عقد ERC20/BEP20
        if (in_array('transfer', $functions) && in_array('balanceOf', $functions) &&
            in_array('approve', $functions) && in_array('totalSupply', $functions)) {
            $analysis['is_token'] = true;
            $analysis['contract_type'] = 'erc20_token';
        }

        // فحص إذا كان عقد NFT (ERC721/ERC1155)
        if (in_array('tokenURI', $functions) || in_array('uri', $functions)) {
            $analysis['is_nft'] = true;
            $analysis['contract_type'] = 'nft';
        }

        // فحص إذا كان عقد ضمان (Escrow)
        if (strpos($sourceCode, 'escrow') !== false || strpos($contractName, 'escrow') !== false ||
            in_array('createTrade', $functions) || in_array('releaseFunds', $functions)) {
            $analysis['is_escrow'] = true;
            $analysis['contract_type'] = 'escrow';
        }

        // فحص إذا كان عقد DeFi
        if (in_array('stake', $functions) || in_array('unstake', $functions) ||
            in_array('addLiquidity', $functions) || in_array('removeLiquidity', $functions)) {
            $analysis['is_defi'] = true;
            $analysis['contract_type'] = 'defi';
        }

        // فحص إذا كان عقد Proxy
        if (!empty($contractInfo['proxy']) && $contractInfo['proxy'] !== '0') {
            $analysis['is_proxy'] = true;
            $analysis['contract_type'] = 'proxy';
        }

        return $analysis;
    }

    /**
     * تحويل Hex إلى نص
     */
    private function hexToString($hex) {
        $hex = str_replace('0x', '', $hex);
        $string = '';
        for ($i = 0; $i < strlen($hex) - 1; $i += 2) {
            $char = chr(hexdec($hex[$i] . $hex[$i + 1]));
            if ($char !== "\0") {
                $string .= $char;
            }
        }
        return trim($string);
    }

    /**
     * إجراء طلب API
     */
    private function makeApiRequest($url) {
        $context = stream_context_create([
            'http' => [
                'timeout' => 30,
                'user_agent' => 'iKAROS-P2P-Admin/1.0'
            ]
        ]);

        $response = file_get_contents($url, false, $context);

        if ($response === false) {
            throw new Exception('Failed to make API request');
        }

        return json_decode($response, true);
    }

    /**
     * تحليل أمان العقد
     */
    private function analyzeContractSecurity($contractInfo) {
        $sourceCode = strtolower($contractInfo['source_code'] ?? '');
        $security = [
            'is_verified' => !empty($contractInfo['source_code']) && $contractInfo['source_code'] !== 'Contract source code not verified',
            'has_proxy' => !empty($contractInfo['proxy']) && $contractInfo['proxy'] !== '0',
            'optimization_used' => $contractInfo['optimization_used'] === '1',
            'security_issues' => [],
            'security_score' => 0
        ];

        // فحص المشاكل الأمنية الشائعة
        if (strpos($sourceCode, 'selfdestruct') !== false) {
            $security['security_issues'][] = 'Contains selfdestruct function';
        }

        if (strpos($sourceCode, 'delegatecall') !== false) {
            $security['security_issues'][] = 'Uses delegatecall (potential security risk)';
        }

        if (strpos($sourceCode, 'tx.origin') !== false) {
            $security['security_issues'][] = 'Uses tx.origin (security anti-pattern)';
        }

        // حساب نقاط الأمان
        $score = 50; // نقطة البداية
        if ($security['is_verified']) $score += 30;
        if ($security['optimization_used']) $score += 10;
        if (empty($security['security_issues'])) $score += 10;

        $security['security_score'] = min(100, $score);

        return $security;
    }

    /**
     * جلب إحصائيات العقد
     */
    private function getContractStatistics($contractAddress, $chainId) {
        // هذه دالة مبسطة - يمكن توسيعها لجلب إحصائيات أكثر تفصيلاً
        return [
            'transaction_count' => 0,
            'last_activity' => null,
            'creation_date' => null,
            'balance' => '0'
        ];
    }

    /**
     * فحص إذا كانت العملة مستقرة
     */
    private function isStablecoin($tokenInfo) {
        $symbol = strtoupper($tokenInfo['symbol'] ?? '');
        $name = strtoupper($tokenInfo['name'] ?? '');

        $stablecoins = ['USDT', 'USDC', 'BUSD', 'DAI', 'TUSD', 'USDD', 'FRAX', 'LUSD'];

        foreach ($stablecoins as $stable) {
            if (strpos($symbol, $stable) !== false || strpos($name, $stable) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * استخراج العملات المدعومة من أحداث العقد
     */
    private function extractSupportedTokensFromEvents($contractAddress, $chainId) {
        // هذه دالة مبسطة - يمكن توسيعها لتحليل أحداث العقد
        return [];
    }

    /**
     * توليد توصيات بناءً على التحليل
     */
    private function generateRecommendations($contractType, $securityAnalysis) {
        $recommendations = [];

        if (!$securityAnalysis['is_verified']) {
            $recommendations[] = 'Contract source code is not verified. Consider verifying for transparency.';
        }

        if (!empty($securityAnalysis['security_issues'])) {
            $recommendations[] = 'Security issues detected. Review contract carefully before use.';
        }

        if ($contractType['is_token'] && !$contractType['is_proxy']) {
            $recommendations[] = 'This is a standard token contract. Suitable for P2P trading.';
        }

        if ($contractType['is_escrow']) {
            $recommendations[] = 'This is an escrow contract. Perfect for secure P2P transactions.';
        }

        return $recommendations;
    }

    /**
     * التأكد من وجود الشبكة في قاعدة البيانات
     */
    private function ensureNetworkExists($chainId, $networkInfo) {
        $pdo = $this->db->getConnection();

        // البحث عن الشبكة
        $stmt = $pdo->prepare("SELECT id FROM supported_networks WHERE chain_id = ?");
        $stmt->execute([$chainId]);
        $network = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($network) {
            return $network['id'];
        }

        // إنشاء شبكة جديدة
        $stmt = $pdo->prepare("
            INSERT INTO supported_networks
            (network_name, network_symbol, chain_id, rpc_url, explorer_url, is_testnet, is_active)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");

        $isTestnet = strpos(strtolower($networkInfo['name']), 'test') !== false;
        $rpcUrl = $this->getDefaultRpcUrl($chainId);

        $stmt->execute([
            $networkInfo['name'],
            $networkInfo['native_currency'],
            $chainId,
            $rpcUrl,
            $networkInfo['explorer_url'],
            $isTestnet,
            true
        ]);

        return $pdo->lastInsertId();
    }

    /**
     * حفظ العقد المحسن
     */
    private function saveEnhancedContract($networkId, $analysisResult) {
        $pdo = $this->db->getConnection();

        // تحديد نوع العقد
        $contractType = 'escrow_integrator'; // افتراضي
        if ($analysisResult['contract_type']['is_escrow']) {
            $contractType = 'core_escrow';
        } elseif ($analysisResult['contract_type']['contract_type'] === 'erc20_token') {
            $contractType = 'escrow_integrator';
        }

        $stmt = $pdo->prepare("
            INSERT INTO enhanced_contracts
            (network_id, contract_type, contract_address, contract_version, is_active, abi_hash)
            VALUES (?, ?, ?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE
            contract_version = VALUES(contract_version),
            is_active = VALUES(is_active),
            abi_hash = VALUES(abi_hash),
            updated_at = CURRENT_TIMESTAMP
        ");

        $abiHash = !empty($analysisResult['contract_info']['abi']) ?
                   md5($analysisResult['contract_info']['abi']) : null;

        $stmt->execute([
            $networkId,
            $contractType,
            $analysisResult['contract_address'],
            '1.0.0',
            true,
            $abiHash
        ]);

        return $pdo->lastInsertId() ?: $this->getContractId($networkId, $analysisResult['contract_address']);
    }

    /**
     * حفظ العملات المدعومة
     */
    private function saveSupportedTokens($networkId, $tokens) {
        $pdo = $this->db->getConnection();
        $tokenIds = [];

        foreach ($tokens as $token) {
            $stmt = $pdo->prepare("
                INSERT INTO supported_tokens
                (network_id, token_address, token_symbol, token_name, decimals, is_stablecoin, is_active)
                VALUES (?, ?, ?, ?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE
                token_symbol = VALUES(token_symbol),
                token_name = VALUES(token_name),
                decimals = VALUES(decimals),
                is_stablecoin = VALUES(is_stablecoin),
                is_active = VALUES(is_active),
                updated_at = CURRENT_TIMESTAMP
            ");

            $stmt->execute([
                $networkId,
                $token['contract_address'],
                $token['symbol'] ?? 'UNKNOWN',
                $token['name'] ?? 'Unknown Token',
                $token['decimals'] ?? 18,
                $token['is_stablecoin'] ?? false,
                true
            ]);

            $tokenIds[] = $pdo->lastInsertId() ?: $this->getTokenId($networkId, $token['contract_address']);
        }

        return $tokenIds;
    }

    /**
     * الحصول على معرف العقد
     */
    private function getContractId($networkId, $contractAddress) {
        $pdo = $this->db->getConnection();
        $stmt = $pdo->prepare("SELECT id FROM enhanced_contracts WHERE network_id = ? AND contract_address = ?");
        $stmt->execute([$networkId, $contractAddress]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result ? $result['id'] : null;
    }

    /**
     * الحصول على معرف العملة
     */
    private function getTokenId($networkId, $tokenAddress) {
        $pdo = $this->db->getConnection();
        $stmt = $pdo->prepare("SELECT id FROM supported_tokens WHERE network_id = ? AND token_address = ?");
        $stmt->execute([$networkId, $tokenAddress]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result ? $result['id'] : null;
    }

    /**
     * الحصول على RPC URL افتراضي
     */
    private function getDefaultRpcUrl($chainId) {
        $defaultRpcs = [
            1 => 'https://mainnet.infura.io/v3/YOUR_PROJECT_ID',
            56 => 'https://bsc-dataseed.binance.org/',
            97 => 'https://data-seed-prebsc-1-s1.binance.org:8545/',
            137 => 'https://polygon-rpc.com/',
            80001 => 'https://rpc-mumbai.maticvigil.com/'
        ];

        return $defaultRpcs[$chainId] ?? 'https://localhost:8545';
    }

    /**
     * التحقق من صحة عنوان العقد
     */
    private function isValidAddress($address) {
        return preg_match('/^0x[a-fA-F0-9]{40}$/', $address);
    }
}

// تشغيل API
try {
    $api = new ContractVerificationAPI();
    $api->handleRequest();
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Internal server error: ' . $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
}
?>
