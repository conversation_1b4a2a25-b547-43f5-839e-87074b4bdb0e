/**
 * Next.js API Route Proxy for Tokens
 * بروكسي Next.js للعملات
 */

import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // استخراج المعاملات من URL
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || '';
    const active = searchParams.get('active') || '';
    const networkId = searchParams.get('network_id') || '';

    // بناء URL للـ PHP API - استخدام token-management.php مع action=list
    const phpApiUrl = new URL('http://localhost/ikaros-p2p/api/enhanced-contracts/token-management.php');
    phpApiUrl.searchParams.set('action', 'list');
    if (active) phpApiUrl.searchParams.set('active', active);
    if (networkId) phpApiUrl.searchParams.set('network_id', networkId);

    console.log('🔄 Proxying tokens request to:', phpApiUrl.toString());

    // إرسال الطلب إلى PHP API
    const response = await fetch(phpApiUrl.toString(), {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    console.log('📡 Tokens API Response Status:', response.status);

    if (!response.ok) {
      throw new Error(`Tokens API returned ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    console.log('📊 Tokens API Data:', data);

    // إرجاع البيانات مع headers صحيحة
    return NextResponse.json(data, {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });

  } catch (error) {
    console.error('❌ Tokens API Error:', error);
    
    // إرجاع بيانات احتياطية في حالة الخطأ
    const fallbackData = {
      success: false,
      error: 'فشل في الاتصال بـ API العملات',
      error_en: 'Failed to connect to Tokens API',
      data: {
        tokens: [],
        count: 0,
        filters: {
          network_id: null,
          active_only: true,
          stablecoin_only: false
        },
        summary: {
          total_tokens: 0,
          active_tokens: 0,
          stablecoins: 0,
          total_market_cap: 0,
          total_volume_24h: 0
        }
      },
      timestamp: new Date().toISOString(),
      source: 'fallback'
    };

    return NextResponse.json(fallbackData, {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    console.log('🔄 Proxying tokens POST request to PHP API');

    // إرسال الطلب إلى PHP API
    const response = await fetch('http://localhost/ikaros-p2p/api/enhanced-contracts/token-management.php', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify(body),
    });

    console.log('📡 Tokens POST API Response Status:', response.status);

    if (!response.ok) {
      throw new Error(`Tokens POST API returned ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    console.log('📊 Tokens POST API Data:', data);

    return NextResponse.json(data, {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });

  } catch (error) {
    console.error('❌ Tokens POST API Error:', error);
    
    return NextResponse.json({
      success: false,
      error: 'فشل في معالجة طلب POST للعملات',
      error_en: 'Failed to process Tokens POST request',
      timestamp: new Date().toISOString()
    }, {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });
  }
}

export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
