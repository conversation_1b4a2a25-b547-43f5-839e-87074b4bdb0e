-- قاعدة بيانات منصة إيكاروس P2P
-- MySQL Schema

-- تعطيل فحص المفاتيح الخارجية مؤقتاً
SET FOREIGN_KEY_CHECKS = 0;

-- تعيين وضع SQL للتوافق
SET sql_mode = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION';

-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS ikaros_p2p CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE ikaros_p2p;

-- جدول المستخدمين
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    wallet_address VARCHAR(42) UNIQUE NULL,
    username VARCHAR(50) UNIQUE NULL,
    email VARCHAR(100) UNIQUE NULL,
    phone VARCHAR(20),
    full_name VA<PERSON>HA<PERSON>(100),
    password_hash VARCHAR(255) NULL,
    country_code VARCHAR(3),
    is_verified BOOLEAN DEFAULT FALSE,
    is_admin BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    profile_image VARCHAR(255),
    bio TEXT,
    telegram_username VARCHAR(50),
    whatsapp_number VARCHAR(20),
    total_trades INT DEFAULT 0,
    completed_trades INT DEFAULT 0,
    total_volume DECIMAL(20, 8) DEFAULT 0,
    rating DECIMAL(3, 2) DEFAULT 0,
    rating_count INT DEFAULT 0,
    language_preference VARCHAR(10) DEFAULT 'ar',
    theme_preference VARCHAR(10) DEFAULT 'light',
    notification_preferences JSON DEFAULT NULL,
    privacy_settings JSON DEFAULT NULL,
    security_settings JSON DEFAULT NULL,
    email_verified_at TIMESTAMP NULL DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL DEFAULT NULL,

    INDEX idx_wallet_address (wallet_address),
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_country (country_code),
    INDEX idx_verified (is_verified),
    INDEX idx_active (is_active),
    INDEX idx_email_verified (email_verified_at)
);

-- جدول الشبكات المدعومة (جديد للعقد المحسن)
CREATE TABLE supported_networks (
    id INT PRIMARY KEY AUTO_INCREMENT,
    network_name VARCHAR(50) NOT NULL UNIQUE,
    network_symbol VARCHAR(10) NOT NULL,
    chain_id INT NOT NULL UNIQUE,
    rpc_url VARCHAR(255) NOT NULL,
    explorer_url VARCHAR(255) NOT NULL,
    is_testnet BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    gas_price_gwei DECIMAL(10, 2) DEFAULT 5.0,
    block_time_seconds INT DEFAULT 3,
    confirmation_blocks INT DEFAULT 12,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_network_name (network_name),
    INDEX idx_chain_id (chain_id),
    INDEX idx_is_active (is_active),
    INDEX idx_is_testnet (is_testnet)
);

-- جدول العملات المدعومة (جديد للعقد المحسن)
CREATE TABLE supported_tokens (
    id INT PRIMARY KEY AUTO_INCREMENT,
    network_id INT NOT NULL,
    token_address VARCHAR(42) NOT NULL,
    token_symbol VARCHAR(10) NOT NULL,
    token_name VARCHAR(100) NOT NULL,
    decimals TINYINT NOT NULL DEFAULT 18,
    is_stablecoin BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    icon_url VARCHAR(255),
    coingecko_id VARCHAR(100),
    min_trade_amount DECIMAL(20, 8) DEFAULT 1.0,
    max_trade_amount DECIMAL(20, 8) DEFAULT 1000000.0,
    daily_volume_limit DECIMAL(20, 8) DEFAULT 100000.0,
    platform_fee_rate DECIMAL(5, 4) DEFAULT 0.0050, -- 0.5%
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (network_id) REFERENCES supported_networks(id) ON DELETE CASCADE,
    UNIQUE KEY unique_token_network (network_id, token_address),
    INDEX idx_network_id (network_id),
    INDEX idx_token_address (token_address),
    INDEX idx_token_symbol (token_symbol),
    INDEX idx_is_stablecoin (is_stablecoin),
    INDEX idx_is_active (is_active)
);

-- جدول عناوين العقود المحسنة (جديد)
CREATE TABLE enhanced_contracts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    network_id INT NOT NULL,
    contract_type ENUM('core_escrow', 'reputation_manager', 'oracle_manager', 'admin_manager', 'escrow_integrator') NOT NULL,
    contract_address VARCHAR(42) NOT NULL,
    contract_version VARCHAR(20) DEFAULT '1.0.0',
    deployment_block BIGINT,
    deployment_tx_hash VARCHAR(66),
    is_active BOOLEAN DEFAULT TRUE,
    abi_hash VARCHAR(64), -- للتحقق من تطابق ABI
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (network_id) REFERENCES supported_networks(id) ON DELETE CASCADE,
    UNIQUE KEY unique_contract_network (network_id, contract_type),
    INDEX idx_network_id (network_id),
    INDEX idx_contract_type (contract_type),
    INDEX idx_contract_address (contract_address),
    INDEX idx_is_active (is_active)
);

-- جدول العروض (محدث للتكامل مع العقد المحسن)
CREATE TABLE offers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    offer_type ENUM('buy', 'sell') NOT NULL,

    -- معلومات العملة والشبكة (محدث للعقد المحسن)
    network_id INT NOT NULL,
    token_id INT NOT NULL,
    amount DECIMAL(20, 8) NOT NULL,
    min_amount DECIMAL(20, 8),
    max_amount DECIMAL(20, 8),
    price DECIMAL(10, 4) NOT NULL,
    currency VARCHAR(3) NOT NULL,

    -- معلومات إضافية
    payment_methods JSON,
    terms TEXT,
    auto_reply TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    is_premium BOOLEAN DEFAULT FALSE,
    is_free BOOLEAN DEFAULT FALSE,
    views_count INT DEFAULT 0,
    time_limit INT DEFAULT 1800, -- 30 دقيقة بالثواني

    -- حقول التكامل مع العقد الذكي المحسن
    blockchain_trade_id INT UNIQUE NULL,
    transaction_hash VARCHAR(66) NULL,
    contract_status ENUM('pending', 'created', 'joined', 'payment_sent', 'payment_confirmed', 'completed', 'cancelled', 'disputed', 'resolved') DEFAULT 'pending',
    escrow_amount DECIMAL(20, 8) NULL,
    platform_fee DECIMAL(20, 8) NULL,
    net_amount DECIMAL(20, 8) NULL,

    -- معلومات العقد المحسن
    core_escrow_address VARCHAR(42) NULL,
    reputation_score_at_creation INT DEFAULT 100,
    oracle_price_at_creation DECIMAL(20, 8) NULL,

    -- مزامنة البيانات
    last_sync_at TIMESTAMP NULL,
    contract_created_at TIMESTAMP NULL,
    sync_status ENUM('synced', 'pending', 'failed', 'conflict') DEFAULT 'pending',

    -- timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NULL DEFAULT NULL,

    -- المفاتيح الخارجية
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (network_id) REFERENCES supported_networks(id) ON DELETE RESTRICT,
    FOREIGN KEY (token_id) REFERENCES supported_tokens(id) ON DELETE RESTRICT,

    -- فهارس أساسية
    INDEX idx_user_id (user_id),
    INDEX idx_offer_type (offer_type),
    INDEX idx_currency (currency),
    INDEX idx_network_id (network_id),
    INDEX idx_token_id (token_id),
    INDEX idx_active (is_active),
    INDEX idx_premium (is_premium),
    INDEX idx_free (is_free),
    INDEX idx_created_at (created_at),
    INDEX idx_price (price),

    -- فهارس التكامل مع العقد الذكي المحسن
    INDEX idx_blockchain_trade_id (blockchain_trade_id),
    INDEX idx_contract_status (contract_status),
    INDEX idx_transaction_hash (transaction_hash),
    INDEX idx_last_sync (last_sync_at),
    INDEX idx_contract_created (contract_created_at),
    INDEX idx_sync_status (sync_status),
    INDEX idx_core_escrow_address (core_escrow_address),

    -- فهارس مركبة للأداء المحسن
    INDEX idx_active_type_currency (is_active, offer_type, currency),
    INDEX idx_network_token_active (network_id, token_id, is_active),
    INDEX idx_contract_status_sync (contract_status, last_sync_at),
    INDEX idx_user_active_type (user_id, is_active, offer_type),
    INDEX idx_token_price_active (token_id, price, is_active),
    INDEX idx_network_currency_type (network_id, currency, offer_type)
);

-- جدول الصفقات (محدث للتكامل مع العقد المحسن)
CREATE TABLE trades (
    id INT PRIMARY KEY AUTO_INCREMENT,
    blockchain_trade_id INT UNIQUE, -- ID من العقد الذكي المحسن
    offer_id INT NOT NULL,
    offer_blockchain_id INT NULL, -- ربط مع blockchain_trade_id في جدول offers
    seller_id INT NOT NULL,
    buyer_id INT NOT NULL,

    -- معلومات العملة والشبكة (محدث للعقد المحسن)
    network_id INT NOT NULL,
    token_id INT NOT NULL,
    amount DECIMAL(20, 8) NOT NULL,
    price DECIMAL(10, 4) NOT NULL,
    currency VARCHAR(3) NOT NULL,
    total_value DECIMAL(20, 8) NOT NULL,
    platform_fee DECIMAL(20, 8) NOT NULL DEFAULT 0,
    net_amount DECIMAL(20, 8) NOT NULL,

    -- حالات الصفقة المحسنة
    status ENUM('created', 'joined', 'payment_sent', 'payment_confirmed', 'completed', 'cancelled', 'disputed', 'resolved') DEFAULT 'created',
    contract_status ENUM('Created', 'Joined', 'PaymentSent', 'PaymentConfirmed', 'Completed', 'Cancelled', 'Disputed', 'Resolved') DEFAULT 'Created',

    -- تفاصيل الدفع
    payment_method VARCHAR(50),
    payment_details JSON,
    selected_payment_methods JSON, -- طرق الدفع المختارة من العرض
    seller_confirmed BOOLEAN DEFAULT FALSE,
    buyer_confirmed BOOLEAN DEFAULT FALSE,

    -- إدارة النزاعات
    dispute_reason TEXT,
    dispute_resolved_by INT NULL,
    dispute_resolution TEXT,
    dispute_created_at TIMESTAMP NULL,
    dispute_resolved_at TIMESTAMP NULL,

    -- معلومات العقد الذكي المحسن
    create_transaction_hash VARCHAR(66),
    join_transaction_hash VARCHAR(66),
    payment_sent_transaction_hash VARCHAR(66),
    payment_confirmed_transaction_hash VARCHAR(66),
    complete_transaction_hash VARCHAR(66),
    cancel_transaction_hash VARCHAR(66),
    dispute_transaction_hash VARCHAR(66),
    resolve_transaction_hash VARCHAR(66),

    -- عناوين العقود المستخدمة
    core_escrow_address VARCHAR(42),
    reputation_manager_address VARCHAR(42),
    oracle_manager_address VARCHAR(42),
    admin_manager_address VARCHAR(42),

    -- معلومات السمعة والأوراكل
    seller_reputation_before INT,
    buyer_reputation_before INT,
    seller_reputation_after INT,
    buyer_reputation_after INT,
    oracle_price_at_creation DECIMAL(20, 8),
    oracle_price_at_completion DECIMAL(20, 8),

    -- timestamps العقد الذكي المحسن
    contract_created_at TIMESTAMP NULL,
    contract_joined_at TIMESTAMP NULL,
    contract_payment_sent_at TIMESTAMP NULL,
    contract_payment_confirmed_at TIMESTAMP NULL,
    contract_completed_at TIMESTAMP NULL,
    contract_disputed_at TIMESTAMP NULL,
    contract_resolved_at TIMESTAMP NULL,

    -- مزامنة البيانات المحسنة
    last_sync_at TIMESTAMP NULL,
    sync_status ENUM('synced', 'pending', 'failed', 'conflict') DEFAULT 'pending',
    sync_attempts INT DEFAULT 0,
    last_sync_error TEXT NULL,

    -- timestamps عامة
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NULL DEFAULT NULL,
    completed_at TIMESTAMP NULL DEFAULT NULL,

    -- المفاتيح الخارجية المحسنة
    FOREIGN KEY (offer_id) REFERENCES offers(id),
    FOREIGN KEY (offer_blockchain_id) REFERENCES offers(blockchain_trade_id),
    FOREIGN KEY (seller_id) REFERENCES users(id),
    FOREIGN KEY (buyer_id) REFERENCES users(id),
    FOREIGN KEY (dispute_resolved_by) REFERENCES users(id),
    FOREIGN KEY (network_id) REFERENCES supported_networks(id) ON DELETE RESTRICT,
    FOREIGN KEY (token_id) REFERENCES supported_tokens(id) ON DELETE RESTRICT,

    -- فهارس أساسية محسنة
    INDEX idx_blockchain_trade_id (blockchain_trade_id),
    INDEX idx_offer_id (offer_id),
    INDEX idx_offer_blockchain_id (offer_blockchain_id),
    INDEX idx_seller_id (seller_id),
    INDEX idx_buyer_id (buyer_id),
    INDEX idx_network_id (network_id),
    INDEX idx_token_id (token_id),
    INDEX idx_status (status),
    INDEX idx_contract_status (contract_status),
    INDEX idx_created_at (created_at),

    -- فهارس المعاملات المحسنة
    INDEX idx_create_tx (create_transaction_hash),
    INDEX idx_join_tx (join_transaction_hash),
    INDEX idx_payment_sent_tx (payment_sent_transaction_hash),
    INDEX idx_payment_confirmed_tx (payment_confirmed_transaction_hash),
    INDEX idx_complete_tx (complete_transaction_hash),
    INDEX idx_dispute_tx (dispute_transaction_hash),
    INDEX idx_resolve_tx (resolve_transaction_hash),

    -- فهارس عناوين العقود
    INDEX idx_core_escrow_address (core_escrow_address),
    INDEX idx_reputation_manager_address (reputation_manager_address),

    -- فهارس المزامنة المحسنة
    INDEX idx_last_sync (last_sync_at),
    INDEX idx_sync_status (sync_status),
    INDEX idx_sync_attempts (sync_attempts),

    -- فهارس مركبة للأداء المحسن
    INDEX idx_status_created (status, created_at),
    INDEX idx_network_token_status (network_id, token_id, status),
    INDEX idx_contract_status_sync (contract_status, last_sync_at),
    INDEX idx_seller_status (seller_id, status),
    INDEX idx_buyer_status (buyer_id, status)
);

-- جدول أحداث العقد الذكي المحسن (محدث)
CREATE TABLE enhanced_contract_events (
    id INT PRIMARY KEY AUTO_INCREMENT,
    network_id INT NOT NULL,
    contract_type ENUM('core_escrow', 'reputation_manager', 'oracle_manager', 'admin_manager', 'escrow_integrator') NOT NULL,
    contract_address VARCHAR(42) NOT NULL,
    event_type ENUM('TradeCreated', 'TradeJoined', 'PaymentSent', 'PaymentConfirmed', 'TradeCompleted', 'TradeCancelled', 'TradeDisputed', 'DisputeResolved', 'ReputationUpdated', 'PriceUpdated', 'TokenAdded', 'TokenRemoved') NOT NULL,
    blockchain_trade_id INT NULL, -- قد يكون NULL للأحداث غير المرتبطة بصفقة
    transaction_hash VARCHAR(66) NOT NULL,
    block_number BIGINT NOT NULL,
    block_hash VARCHAR(66) NOT NULL,
    log_index INT NOT NULL,

    -- بيانات الحدث المحسنة
    seller_address VARCHAR(42),
    buyer_address VARCHAR(42),
    token_address VARCHAR(42),
    amount DECIMAL(20, 8),
    fee_amount DECIMAL(20, 8),
    price_per_token DECIMAL(20, 8),
    currency VARCHAR(10),

    -- معلومات إضافية محسنة
    event_data JSON,
    raw_log_data TEXT,
    gas_used BIGINT,
    gas_price BIGINT,
    effective_gas_price BIGINT,

    -- حالة المعالجة المحسنة
    processed BOOLEAN DEFAULT FALSE,
    processed_at TIMESTAMP NULL,
    processing_attempts INT DEFAULT 0,
    error_message TEXT NULL,
    retry_after TIMESTAMP NULL,

    -- timestamps
    event_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- المفاتيح الخارجية
    FOREIGN KEY (network_id) REFERENCES supported_networks(id) ON DELETE CASCADE,

    -- فهارس محسنة
    UNIQUE KEY unique_event_log (transaction_hash, log_index),
    INDEX idx_network_id (network_id),
    INDEX idx_contract_type (contract_type),
    INDEX idx_contract_address (contract_address),
    INDEX idx_event_type (event_type),
    INDEX idx_blockchain_trade_id (blockchain_trade_id),
    INDEX idx_transaction_hash (transaction_hash),
    INDEX idx_block_number (block_number),
    INDEX idx_seller_address (seller_address),
    INDEX idx_buyer_address (buyer_address),
    INDEX idx_token_address (token_address),
    INDEX idx_processed (processed),
    INDEX idx_processing_attempts (processing_attempts),
    INDEX idx_event_timestamp (event_timestamp),
    INDEX idx_created_at (created_at),
    INDEX idx_retry_after (retry_after),

    -- فهارس مركبة محسنة
    INDEX idx_network_contract_type (network_id, contract_type),
    INDEX idx_contract_event_type (contract_address, event_type),
    INDEX idx_processed_retry (processed, retry_after),
    INDEX idx_trade_event_type (blockchain_trade_id, event_type),
    INDEX idx_processed_timestamp (processed, event_timestamp)
);

-- جدول سجل تحديثات الشبكات والعملات (جديد)
CREATE TABLE network_token_updates (
    id INT PRIMARY KEY AUTO_INCREMENT,
    update_type ENUM('network_added', 'network_updated', 'network_removed', 'token_added', 'token_updated', 'token_removed') NOT NULL,
    network_id INT,
    token_id INT,
    admin_id INT NOT NULL,
    old_data JSON,
    new_data JSON,
    update_source ENUM('manual', 'blockchain_api', 'contract_verification') NOT NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (network_id) REFERENCES supported_networks(id) ON DELETE SET NULL,
    FOREIGN KEY (token_id) REFERENCES supported_tokens(id) ON DELETE SET NULL,
    FOREIGN KEY (admin_id) REFERENCES admin_users(id) ON DELETE CASCADE,
    INDEX idx_update_type (update_type),
    INDEX idx_network_id (network_id),
    INDEX idx_token_id (token_id),
    INDEX idx_admin_id (admin_id),
    INDEX idx_update_source (update_source),
    INDEX idx_created_at (created_at)
);

-- جدول إعدادات الشبكات المتقدمة (جديد)
CREATE TABLE network_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    network_id INT NOT NULL,
    setting_key VARCHAR(100) NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (network_id) REFERENCES supported_networks(id) ON DELETE CASCADE,
    UNIQUE KEY unique_network_setting (network_id, setting_key),
    INDEX idx_network_id (network_id),
    INDEX idx_setting_key (setting_key),
    INDEX idx_is_active (is_active)
);

-- جدول حالة المزامنة (جديد)
CREATE TABLE sync_status (
    id INT PRIMARY KEY AUTO_INCREMENT,
    entity_type ENUM('offer', 'trade', 'contract_event') NOT NULL,
    entity_id INT NOT NULL,
    blockchain_id INT NULL,

    -- حالة المزامنة
    sync_status ENUM('pending', 'syncing', 'synced', 'failed', 'conflict') DEFAULT 'pending',
    last_sync_attempt TIMESTAMP NULL,
    last_successful_sync TIMESTAMP NULL,
    sync_attempts INT DEFAULT 0,
    max_attempts INT DEFAULT 5,

    -- تفاصيل الخطأ
    error_message TEXT NULL,
    error_code VARCHAR(50) NULL,

    -- بيانات المزامنة
    local_hash VARCHAR(64) NULL,
    blockchain_hash VARCHAR(64) NULL,
    conflict_data JSON NULL,

    -- timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- مفاتيح فريدة
    UNIQUE KEY unique_entity (entity_type, entity_id),

    -- فهارس
    INDEX idx_entity_type (entity_type),
    INDEX idx_entity_id (entity_id),
    INDEX idx_blockchain_id (blockchain_id),
    INDEX idx_sync_status (sync_status),
    INDEX idx_last_sync_attempt (last_sync_attempt),
    INDEX idx_last_successful_sync (last_successful_sync),
    INDEX idx_sync_attempts (sync_attempts),

    -- فهارس مركبة
    INDEX idx_status_attempts (sync_status, sync_attempts),
    INDEX idx_entity_status (entity_type, sync_status)
);

-- جدول أسعار العملات من Oracle Manager (جديد)
CREATE TABLE oracle_price_feeds (
    id INT PRIMARY KEY AUTO_INCREMENT,
    network_id INT NOT NULL,
    token_id INT NOT NULL,
    currency VARCHAR(10) NOT NULL,

    -- بيانات السعر
    price DECIMAL(20, 8) NOT NULL,
    confidence_level INT DEFAULT 9500, -- 0-10000 (0-100%)
    deviation_percentage DECIMAL(5, 4) DEFAULT 0.0000, -- انحراف السعر

    -- معلومات التحديث
    last_updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(42), -- عنوان محدث السعر
    heartbeat_interval INT DEFAULT 3600, -- فترة التحديث بالثواني
    max_price_age INT DEFAULT 3600, -- أقصى عمر للسعر بالثواني

    -- حالة السعر
    is_active BOOLEAN DEFAULT TRUE,
    is_stale BOOLEAN DEFAULT FALSE, -- السعر قديم

    -- timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- المفاتيح الخارجية
    FOREIGN KEY (network_id) REFERENCES supported_networks(id) ON DELETE CASCADE,
    FOREIGN KEY (token_id) REFERENCES supported_tokens(id) ON DELETE CASCADE,

    -- فهارس
    UNIQUE KEY unique_token_currency_network (network_id, token_id, currency),
    INDEX idx_network_id (network_id),
    INDEX idx_token_id (token_id),
    INDEX idx_currency (currency),
    INDEX idx_price (price),
    INDEX idx_last_updated (last_updated_at),
    INDEX idx_is_active (is_active),
    INDEX idx_is_stale (is_stale),
    INDEX idx_confidence (confidence_level)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول تسجيل أنشطة APIs المحسنة (جديد)
CREATE TABLE api_activity_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,

    -- معلومات الطلب
    api_endpoint VARCHAR(255) NOT NULL,
    request_method ENUM('GET', 'POST', 'PUT', 'DELETE') NOT NULL DEFAULT 'GET',
    request_params JSON DEFAULT NULL,

    -- معلومات الاستجابة
    response_status INT NOT NULL,
    response_time_ms INT NOT NULL,
    response_size_bytes INT DEFAULT NULL,

    -- معلومات المستخدم
    admin_id INT DEFAULT NULL,
    user_ip VARCHAR(45) DEFAULT NULL,
    user_agent TEXT DEFAULT NULL,

    -- معلومات إضافية
    error_message TEXT DEFAULT NULL,
    blockchain_network VARCHAR(50) DEFAULT NULL,
    contract_address VARCHAR(42) DEFAULT NULL,

    -- التوقيت
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- فهارس
    INDEX idx_endpoint (api_endpoint),
    INDEX idx_status (response_status),
    INDEX idx_admin_id (admin_id),
    INDEX idx_created_at (created_at),
    INDEX idx_network (blockchain_network),
    INDEX idx_response_time (response_time_ms)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول أسعار صرف العملات المحلية (جديد)
CREATE TABLE fiat_exchange_rates (
    id INT PRIMARY KEY AUTO_INCREMENT,
    base_currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    target_currency VARCHAR(3) NOT NULL,

    -- بيانات سعر الصرف
    exchange_rate DECIMAL(15, 8) NOT NULL,
    inverse_rate DECIMAL(15, 8) NOT NULL, -- السعر العكسي

    -- معلومات التحديث
    last_updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(42), -- عنوان محدث السعر
    source VARCHAR(50) DEFAULT 'manual', -- مصدر السعر

    -- حالة السعر
    is_active BOOLEAN DEFAULT TRUE,
    confidence_level INT DEFAULT 9500, -- 0-10000 (0-100%)

    -- timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- فهارس
    UNIQUE KEY unique_currency_pair (base_currency, target_currency),
    INDEX idx_base_currency (base_currency),
    INDEX idx_target_currency (target_currency),
    INDEX idx_exchange_rate (exchange_rate),
    INDEX idx_last_updated (last_updated_at),
    INDEX idx_is_active (is_active),
    INDEX idx_confidence (confidence_level)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول تقييمات المستخدمين المحسن (جديد)
CREATE TABLE enhanced_user_ratings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    trade_id INT NOT NULL,
    rater_id INT NOT NULL, -- المقيم
    rated_id INT NOT NULL, -- المقيم
    network_id INT NOT NULL,

    -- بيانات التقييم
    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5), -- 1-5 نجوم
    rating_normalized INT NOT NULL, -- التقييم مضروب في 200 للدقة (200-1000)
    comment TEXT,

    -- معلومات إضافية
    trade_role ENUM('seller', 'buyer') NOT NULL, -- دور المقيم في الصفقة
    is_verified BOOLEAN DEFAULT FALSE, -- تم التحقق من التقييم
    is_valid BOOLEAN DEFAULT TRUE,

    -- معلومات العقد الذكي
    blockchain_rating_id VARCHAR(64), -- معرف التقييم في العقد الذكي
    transaction_hash VARCHAR(66),

    -- timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- المفاتيح الخارجية
    FOREIGN KEY (trade_id) REFERENCES trades(id) ON DELETE CASCADE,
    FOREIGN KEY (rater_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (rated_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (network_id) REFERENCES supported_networks(id) ON DELETE CASCADE,

    -- فهارس
    UNIQUE KEY unique_trade_rater (trade_id, rater_id),
    INDEX idx_trade_id (trade_id),
    INDEX idx_rater_id (rater_id),
    INDEX idx_rated_id (rated_id),
    INDEX idx_network_id (network_id),
    INDEX idx_rating (rating),
    INDEX idx_is_verified (is_verified),
    INDEX idx_is_valid (is_valid),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول تكامل العقود المحسنة (جديد)
CREATE TABLE contract_integration_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    trade_id INT NOT NULL,
    network_id INT NOT NULL,

    -- معلومات التكامل
    integration_type ENUM('trade_creation', 'trade_join', 'payment_sent', 'payment_confirmed', 'trade_completion', 'dispute_creation', 'dispute_resolution', 'reputation_update', 'price_validation') NOT NULL,
    contract_type ENUM('core_escrow', 'reputation_manager', 'oracle_manager', 'admin_manager', 'escrow_integrator') NOT NULL,

    -- حالة التكامل
    status ENUM('pending', 'success', 'failed', 'retry') DEFAULT 'pending',
    attempts INT DEFAULT 1,
    max_attempts INT DEFAULT 3,

    -- بيانات التكامل
    input_data JSON,
    output_data JSON,
    error_message TEXT,

    -- معلومات العقد الذكي
    transaction_hash VARCHAR(66),
    block_number BIGINT,
    gas_used BIGINT,
    gas_price BIGINT,

    -- timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,

    -- المفاتيح الخارجية
    FOREIGN KEY (trade_id) REFERENCES trades(id) ON DELETE CASCADE,
    FOREIGN KEY (network_id) REFERENCES supported_networks(id) ON DELETE CASCADE,

    -- فهارس
    INDEX idx_trade_id (trade_id),
    INDEX idx_network_id (network_id),
    INDEX idx_integration_type (integration_type),
    INDEX idx_contract_type (contract_type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_transaction_hash (transaction_hash),
    INDEX idx_status_attempts (status, attempts)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول السمعة المحسن (جديد للعقد المحسن)
CREATE TABLE enhanced_user_reputation (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    network_id INT NOT NULL,

    -- نقاط السمعة
    reputation_score INT DEFAULT 100,
    total_trades INT DEFAULT 0,
    successful_trades INT DEFAULT 0,
    disputed_trades INT DEFAULT 0,
    cancelled_trades INT DEFAULT 0,

    -- إحصائيات التداول
    total_volume DECIMAL(20, 8) DEFAULT 0,
    average_completion_time INT DEFAULT 0, -- بالدقائق
    response_rate DECIMAL(5, 2) DEFAULT 100.0, -- نسبة مئوية

    -- تقييمات المستخدمين
    positive_ratings INT DEFAULT 0,
    neutral_ratings INT DEFAULT 0,
    negative_ratings INT DEFAULT 0,
    average_rating DECIMAL(3, 2) DEFAULT 0,

    -- معلومات إضافية
    first_trade_date TIMESTAMP NULL,
    last_trade_date TIMESTAMP NULL,
    reputation_level ENUM('beginner', 'intermediate', 'advanced', 'expert', 'master') DEFAULT 'beginner',

    -- تحديث السمعة من العقد الذكي
    last_blockchain_update TIMESTAMP NULL,
    blockchain_reputation_hash VARCHAR(64) NULL,

    -- timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- المفاتيح الخارجية
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (network_id) REFERENCES supported_networks(id) ON DELETE CASCADE,

    -- مفتاح فريد
    UNIQUE KEY unique_user_network (user_id, network_id),

    -- فهارس
    INDEX idx_user_id (user_id),
    INDEX idx_network_id (network_id),
    INDEX idx_reputation_score (reputation_score),
    INDEX idx_total_trades (total_trades),
    INDEX idx_reputation_level (reputation_level),
    INDEX idx_average_rating (average_rating),
    INDEX idx_last_trade_date (last_trade_date),

    -- فهارس مركبة
    INDEX idx_reputation_trades (reputation_score, total_trades),
    INDEX idx_network_reputation (network_id, reputation_score),
    INDEX idx_user_reputation_level (user_id, reputation_level)
);

-- جدول الرسائل (الدردشة)
CREATE TABLE messages (
    id INT PRIMARY KEY AUTO_INCREMENT,
    trade_id INT NOT NULL,
    sender_id INT NOT NULL,
    message_type ENUM('text', 'image', 'file', 'payment_proof', 'system', 'deleted') DEFAULT 'text',
    content TEXT NOT NULL,
    file_url VARCHAR(255),
    file_name VARCHAR(255),
    file_size INT,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (trade_id) REFERENCES trades(id) ON DELETE CASCADE,
    FOREIGN KEY (sender_id) REFERENCES users(id),
    INDEX idx_trade_id (trade_id),
    INDEX idx_sender_id (sender_id),
    INDEX idx_created_at (created_at),
    INDEX idx_is_read (is_read)
);

-- جدول التقييمات
CREATE TABLE reviews (
    id INT PRIMARY KEY AUTO_INCREMENT,
    trade_id INT NOT NULL,
    reviewer_id INT NOT NULL,
    reviewed_id INT NOT NULL,
    rating TINYINT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    comment TEXT,
    is_positive BOOLEAN,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (trade_id) REFERENCES trades(id),
    FOREIGN KEY (reviewer_id) REFERENCES users(id),
    FOREIGN KEY (reviewed_id) REFERENCES users(id),
    UNIQUE KEY unique_review (trade_id, reviewer_id),
    INDEX idx_trade_id (trade_id),
    INDEX idx_reviewer_id (reviewer_id),
    INDEX idx_reviewed_id (reviewed_id),
    INDEX idx_rating (rating)
);

-- جدول الإشعارات
CREATE TABLE notifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    type VARCHAR(50) NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    data JSON,
    is_read BOOLEAN DEFAULT FALSE,
    action_url VARCHAR(255) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_is_read (is_read),
    INDEX idx_created_at (created_at)
);

-- جدول إعدادات النظام
CREATE TABLE system_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    updated_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (updated_by) REFERENCES users(id),
    INDEX idx_setting_key (setting_key),
    INDEX idx_is_public (is_public)
);

-- جدول إعدادات المنصة (للإدارة)
CREATE TABLE platform_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'integer', 'float', 'decimal', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    updated_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (updated_by) REFERENCES users(id),
    INDEX idx_setting_key (setting_key),
    INDEX idx_setting_type (setting_type),
    INDEX idx_is_active (is_active)
);

-- جدول المدراء المنفصل (نظام صلاحيات متقدم)
CREATE TABLE admin_users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL UNIQUE,
    admin_role ENUM('super_admin', 'admin', 'moderator', 'support') DEFAULT 'admin',
    permissions JSON NOT NULL DEFAULT '[]',
    department VARCHAR(50) DEFAULT NULL,
    can_manage_users BOOLEAN DEFAULT FALSE,
    can_manage_trades BOOLEAN DEFAULT FALSE,
    can_resolve_disputes BOOLEAN DEFAULT FALSE,
    can_manage_contracts BOOLEAN DEFAULT FALSE,
    can_view_analytics BOOLEAN DEFAULT TRUE,
    can_manage_settings BOOLEAN DEFAULT FALSE,
    can_emergency_actions BOOLEAN DEFAULT FALSE,
    can_manage_admins BOOLEAN DEFAULT FALSE,
    ip_whitelist JSON DEFAULT NULL,
    session_timeout_hours INT DEFAULT 8,
    two_factor_enabled BOOLEAN DEFAULT FALSE,
    two_factor_secret VARCHAR(32) DEFAULT NULL,
    last_login_ip VARCHAR(45) DEFAULT NULL,
    failed_login_attempts INT DEFAULT 0,
    locked_until TIMESTAMP NULL DEFAULT NULL,
    created_by INT DEFAULT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    notes TEXT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES admin_users(id) ON DELETE SET NULL,

    INDEX idx_user_id (user_id),
    INDEX idx_admin_role (admin_role),
    INDEX idx_is_active (is_active),
    INDEX idx_created_by (created_by),
    INDEX idx_last_activity (last_activity),
    INDEX idx_failed_attempts (failed_login_attempts),
    INDEX idx_locked_until (locked_until)
);

-- جدول جلسات المدراء المنفصلة
CREATE TABLE admin_sessions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    admin_user_id INT NOT NULL,
    session_token VARCHAR(128) UNIQUE NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT DEFAULT NULL,
    login_method ENUM('credentials', 'wallet', 'two_factor') DEFAULT 'credentials',
    is_active BOOLEAN DEFAULT TRUE,
    expires_at TIMESTAMP NOT NULL,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (admin_user_id) REFERENCES admin_users(id) ON DELETE CASCADE,

    INDEX idx_admin_user_id (admin_user_id),
    INDEX idx_session_token (session_token),
    INDEX idx_is_active (is_active),
    INDEX idx_expires_at (expires_at),
    INDEX idx_ip_address (ip_address),
    INDEX idx_last_activity (last_activity)
);

-- جدول لتتبع آخر نشاط للمستخدمين
CREATE TABLE user_activity (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    activity_type VARCHAR(50) NOT NULL,
    activity_data JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_activity_type (activity_type),
    INDEX idx_created_at (created_at)
);

-- جدول للإحصائيات المحفوظة (cache)
CREATE TABLE cached_statistics (
    id INT PRIMARY KEY AUTO_INCREMENT,
    stat_key VARCHAR(100) UNIQUE NOT NULL,
    stat_value JSON NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_stat_key (stat_key),
    INDEX idx_expires_at (expires_at)
);

-- جدول سجل النشاطات
CREATE TABLE activity_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    action VARCHAR(100) NOT NULL,
    entity_type VARCHAR(50),
    entity_id INT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_entity (entity_type, entity_id),
    INDEX idx_created_at (created_at)
);

-- جدول الملفات المرفوعة
CREATE TABLE uploaded_files (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT NOT NULL,
    mime_type VARCHAR(100),
    file_type ENUM('profile_image', 'payment_proof', 'document', 'other') DEFAULT 'other',
    is_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_file_type (file_type),
    INDEX idx_created_at (created_at)
);

-- جدول معاملات المحفظة
CREATE TABLE wallet_transactions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    transaction_type ENUM('deposit','withdrawal','trade','fee','refund') NOT NULL,
    amount DECIMAL(20,8) NOT NULL,
    currency VARCHAR(10) NOT NULL DEFAULT 'USDT',
    status ENUM('pending','completed','failed','cancelled') NOT NULL DEFAULT 'pending',
    transaction_hash VARCHAR(255) DEFAULT NULL,
    blockchain_network VARCHAR(50) DEFAULT 'BSC',
    from_address VARCHAR(255) DEFAULT NULL,
    to_address VARCHAR(255) DEFAULT NULL,
    gas_fee DECIMAL(20,8) DEFAULT 0.00000000,
    confirmation_count INT DEFAULT 0,
    required_confirmations INT DEFAULT 12,
    trade_id INT DEFAULT NULL,
    offer_id INT DEFAULT NULL,
    description TEXT DEFAULT NULL,
    metadata JSON DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL DEFAULT NULL,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (trade_id) REFERENCES trades(id) ON DELETE SET NULL,
    FOREIGN KEY (offer_id) REFERENCES offers(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_transaction_type (transaction_type),
    INDEX idx_status (status),
    INDEX idx_transaction_hash (transaction_hash),
    INDEX idx_trade_id (trade_id),
    INDEX idx_offer_id (offer_id),
    INDEX idx_created_at (created_at)
);

-- جدول أرصدة المحفظة
CREATE TABLE wallet_balances (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    currency VARCHAR(10) NOT NULL DEFAULT 'USDT',
    available_balance DECIMAL(20,8) NOT NULL DEFAULT 0.00000000,
    locked_balance DECIMAL(20,8) NOT NULL DEFAULT 0.00000000,
    total_balance DECIMAL(20,8) GENERATED ALWAYS AS (available_balance + locked_balance) STORED,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_currency (user_id, currency),
    INDEX idx_user_id (user_id),
    INDEX idx_currency (currency)
);

-- جدول عناوين المحفظة
CREATE TABLE wallet_addresses (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    address VARCHAR(255) NOT NULL,
    currency VARCHAR(10) NOT NULL DEFAULT 'USDT',
    network VARCHAR(50) NOT NULL DEFAULT 'BSC',
    address_type ENUM('deposit','withdrawal','trading') NOT NULL DEFAULT 'deposit',
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    label VARCHAR(100) DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_used TIMESTAMP NULL DEFAULT NULL,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_address (user_id, address, network),
    INDEX idx_user_id (user_id),
    INDEX idx_address (address),
    INDEX idx_network (network),
    INDEX idx_currency (currency)
);

-- جدول جلسات المستخدمين (محدث للتوافق مع MySQL)
CREATE TABLE user_sessions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    session_token VARCHAR(128) UNIQUE NOT NULL,
    session_type ENUM('user', 'admin') DEFAULT 'user',
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    last_activity TIMESTAMP NULL DEFAULT NULL,
    expires_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_session_token (session_token),
    INDEX idx_session_type (session_type),
    INDEX idx_is_active (is_active),
    INDEX idx_expires_at (expires_at),
    INDEX idx_last_activity (last_activity)
);

-- إدراج إعدادات النظام الافتراضية (محدث للعقد الذكي)
INSERT INTO system_settings (setting_key, setting_value, description, is_public) VALUES
-- إعدادات المنصة الأساسية
('platform_fee', '0.015', 'رسوم المنصة (1.5%)', TRUE),
('min_trade_amount', '10', 'أقل مبلغ للتداول (USDT)', TRUE),
('max_trade_amount', '50000', 'أكبر مبلغ للتداول (USDT)', TRUE),
('default_trade_timeout', '1800', 'مهلة الصفقة الافتراضية (ثانية)', TRUE),
('maintenance_mode', 'false', 'وضع الصيانة', TRUE),
('registration_enabled', 'true', 'تفعيل التسجيل الجديد', TRUE),

-- إعدادات العقود الذكية المحسنة (BSC Testnet)
('core_escrow_address', '******************************************', 'عنوان عقد الضمان الرئيسي', FALSE),
('reputation_manager_address', '0x56A6914523413b0e7344f57466A6239fCC97b913', 'عنوان عقد إدارة السمعة', FALSE),
('oracle_manager_address', '0xB70715392F62628Ccd1258AAF691384bE8C023b6', 'عنوان عقد إدارة الأسعار', FALSE),
('admin_manager_address', '0x5A9FD8082ADA38678721D59AAB4d4F76883c5575', 'عنوان عقد الإدارة', FALSE),
('escrow_integrator_address', '******************************************', 'عنوان عقد التكامل', FALSE),

-- إعدادات العقود القديمة (للتوافق مع النظام القديم)
('escrow_contract_address', '******************************************', 'عنوان عقد الضمان (قديم)', FALSE),
('usdt_contract_address', '******************************************', 'عنوان عقد USDT (BSC Testnet)', FALSE),

-- إعدادات الشبكة والمحافظ
('admin_wallet_address', '******************************************', 'عنوان محفظة المدير', FALSE),
('fee_wallet_address', '******************************************', 'عنوان محفظة الرسوم', FALSE),
('network_chain_id', '97', 'معرف الشبكة (BSC Testnet)', FALSE),
('network_name', 'BSC Testnet', 'اسم الشبكة', TRUE),
('network_rpc_url', 'https://data-seed-prebsc-1-s1.binance.org:8545/', 'رابط RPC للشبكة', FALSE),
('network_explorer_url', 'https://testnet.bscscan.com/', 'رابط مستكشف الشبكة', FALSE),

-- إعدادات المزامنة
('sync_interval', '30', 'فترة المزامنة بالثواني', FALSE),
('max_sync_attempts', '5', 'أقصى عدد محاولات المزامنة', FALSE),
('event_sync_enabled', 'true', 'تفعيل مزامنة أحداث العقد', FALSE),
('auto_sync_enabled', 'true', 'تفعيل المزامنة التلقائية', FALSE),

-- إعدادات APIs المحسنة
('api_cache_ttl', '300', 'مدة تخزين البيانات المؤقت للـ APIs (ثانية)', FALSE),
('blockchain_api_timeout', '30', 'مهلة انتظار APIs البلوك تشين (ثانية)', FALSE),
('contract_verification_enabled', 'true', 'تفعيل التحقق من العقود', FALSE),
('network_health_check_interval', '60', 'فترة فحص صحة الشبكات (ثانية)', FALSE),
('token_price_update_interval', '300', 'فترة تحديث أسعار العملات (ثانية)', FALSE),
('max_concurrent_api_calls', '10', 'أقصى عدد استدعاءات API متزامنة', FALSE),

-- إعدادات العملات والدفع (مرتبة حسب الأهمية)
('supported_currencies', '["SAR", "AED", "KWD", "QAR", "BHD", "OMR", "JOD", "EGP", "USD", "EUR", "GBP", "JPY", "CAD", "AUD", "CHF", "CNY", "TRY", "LBP", "MAD", "TND", "DZD", "IQD", "SYP", "YER", "PKR", "INR", "BDT", "MYR", "IDR", "SGD"]', 'العملات المدعومة', TRUE),
('supported_stablecoins', '["USDT", "USDC", "BUSD", "DAI", "FDUSD", "TUSD", "USDD", "FRAX", "USDP", "LUSD", "GUSD", "SUSD", "USTC", "PYUSD"]', 'العملات المستقرة المدعومة', TRUE),
('default_stablecoin', 'USDT', 'العملة المستقرة الافتراضية', TRUE),

-- إعدادات الأمان
('require_kyc', 'false', 'إلزامية التحقق من الهوية', TRUE),
('max_daily_volume', '100000', 'أقصى حجم تداول يومي (USDT)', TRUE),
('dispute_timeout', '86400', 'مهلة النزاع بالثواني (24 ساعة)', TRUE),
('auto_cancel_timeout', '1800', 'مهلة الإلغاء التلقائي بالثواني (30 دقيقة)', TRUE),
('session_lifetime', '7200', 'مدة الجلسة بالثواني (ساعتان)', FALSE),
('max_login_attempts', '5', 'أقصى عدد محاولات تسجيل الدخول', FALSE),
('lockout_duration', '900', 'مدة الحظر بعد المحاولات الفاشلة (15 دقيقة)', FALSE),
('require_email_verification', 'false', 'إلزامية تأكيد البريد الإلكتروني', TRUE),
('password_min_length', '8', 'أقل طول لكلمة المرور', TRUE),

-- إعدادات الإشعارات
('email_notifications', 'true', 'تفعيل الإشعارات بالبريد الإلكتروني', TRUE),
('push_notifications', 'true', 'تفعيل الإشعارات المباشرة', TRUE),
('sms_notifications', 'false', 'تفعيل الإشعارات بالرسائل النصية', TRUE);

-- إنشاء مستخدم مدير افتراضي (يجب تحديث wallet_address)
-- كلمة المرور الافتراضية: admin123 (مشفرة بـ password_hash)
INSERT INTO users (wallet_address, username, email, full_name, password_hash, is_admin, is_verified, is_active, email_verified_at) VALUES
('******************************************', 'admin', '<EMAIL>', 'مدير النظام', '$2y$10$.r/I2npVAaKeCm8kjdU2L.saGJ/kZ5WtKM.eZaCPZLsiDLNTF9NO6', TRUE, TRUE, TRUE, CURRENT_TIMESTAMP);

-- إضافة المدير الافتراضي إلى جدول المدراء
INSERT INTO admin_users (
    user_id,
    admin_role,
    permissions,
    can_manage_users,
    can_manage_trades,
    can_resolve_disputes,
    can_manage_contracts,
    can_view_analytics,
    can_manage_settings,
    can_emergency_actions,
    can_manage_admins,
    session_timeout_hours,
    notes
) VALUES (
    1, -- معرف المستخدم المدير
    'super_admin',
    '["super_admin", "manage_users", "manage_trades", "resolve_disputes", "manage_contracts", "view_analytics", "manage_settings", "emergency_actions", "manage_admins"]',
    TRUE,
    TRUE,
    TRUE,
    TRUE,
    TRUE,
    TRUE,
    TRUE,
    TRUE,
    8,
    'مدير النظام الافتراضي - تم إنشاؤه تلقائياً'
);



-- إنشاء فهارس إضافية للأداء
CREATE INDEX idx_users_rating ON users(rating DESC);
CREATE INDEX idx_offers_price_currency ON offers(currency, price);
CREATE INDEX idx_trades_status_created ON trades(status, created_at);
CREATE INDEX idx_messages_trade_created ON messages(trade_id, created_at);

-- Views مفيدة للتقارير
CREATE VIEW user_stats AS
SELECT 
    u.id,
    u.username,
    u.wallet_address,
    u.total_trades,
    u.completed_trades,
    u.total_volume,
    u.rating,
    u.rating_count,
    COALESCE(active_offers.count, 0) as active_offers_count,
    COALESCE(active_trades.count, 0) as active_trades_count
FROM users u
LEFT JOIN (
    SELECT user_id, COUNT(*) as count 
    FROM offers 
    WHERE is_active = TRUE 
    GROUP BY user_id
) active_offers ON u.id = active_offers.user_id
LEFT JOIN (
    SELECT 
        CASE 
            WHEN seller_id = u.id THEN seller_id
            WHEN buyer_id = u.id THEN buyer_id
        END as user_id,
        COUNT(*) as count
    FROM trades t, users u
    WHERE (t.seller_id = u.id OR t.buyer_id = u.id)
    AND t.status IN ('created', 'payment_sent', 'payment_received')
    GROUP BY user_id
) active_trades ON u.id = active_trades.user_id;

-- View للإحصائيات العامة (محدث للعقد المحسن)
CREATE VIEW enhanced_platform_stats AS
SELECT
    -- إحصائيات المستخدمين
    (SELECT COUNT(*) FROM users WHERE is_active = TRUE) as total_users,
    (SELECT COUNT(*) FROM users WHERE is_verified = TRUE) as verified_users,
    (SELECT AVG(rating) FROM users WHERE rating > 0) as average_rating,

    -- إحصائيات الشبكات والعملات
    (SELECT COUNT(*) FROM supported_networks WHERE is_active = TRUE) as active_networks,
    (SELECT COUNT(*) FROM supported_tokens WHERE is_active = TRUE) as active_tokens,
    (SELECT COUNT(*) FROM supported_tokens WHERE is_stablecoin = TRUE AND is_active = TRUE) as active_stablecoins,

    -- إحصائيات العروض المحسنة
    (SELECT COUNT(*) FROM offers WHERE is_active = TRUE) as active_offers,
    (SELECT COUNT(*) FROM offers WHERE contract_status = 'created') as contract_created_offers,
    (SELECT COUNT(*) FROM offers WHERE contract_status = 'pending') as pending_offers,
    (SELECT COUNT(*) FROM offers WHERE sync_status = 'synced') as synced_offers,
    (SELECT SUM(amount) FROM offers WHERE is_active = TRUE) as total_offer_volume,

    -- إحصائيات الصفقات المحسنة
    (SELECT COUNT(*) FROM trades) as total_trades,
    (SELECT COUNT(*) FROM trades WHERE status = 'completed') as completed_trades,
    (SELECT COUNT(*) FROM trades WHERE status = 'disputed') as disputed_trades,
    (SELECT COUNT(*) FROM trades WHERE status = 'resolved') as resolved_trades,
    (SELECT SUM(total_value) FROM trades WHERE status = 'completed') as total_trade_volume,
    (SELECT SUM(platform_fee) FROM trades WHERE status = 'completed') as total_fees_collected,

    -- إحصائيات المزامنة المحسنة
    (SELECT COUNT(*) FROM offers WHERE sync_status = 'synced') as synced_offers_count,
    (SELECT COUNT(*) FROM offers WHERE sync_status = 'failed') as failed_offers_sync,
    (SELECT COUNT(*) FROM trades WHERE sync_status = 'synced') as synced_trades_count,
    (SELECT COUNT(*) FROM trades WHERE sync_status = 'failed') as failed_trades_sync,
    (SELECT COUNT(*) FROM enhanced_contract_events WHERE processed = TRUE) as processed_events,
    (SELECT COUNT(*) FROM enhanced_contract_events WHERE processed = FALSE) as pending_events,

    -- إحصائيات السمعة المحسنة
    (SELECT AVG(reputation_score) FROM enhanced_user_reputation) as average_reputation_score,
    (SELECT COUNT(*) FROM enhanced_user_reputation WHERE reputation_level = 'expert') as expert_users,
    (SELECT COUNT(*) FROM enhanced_user_reputation WHERE reputation_level = 'master') as master_users,

    -- إحصائيات العقود المحسنة
    (SELECT COUNT(*) FROM enhanced_contracts WHERE is_active = TRUE) as active_contracts,
    (SELECT COUNT(DISTINCT network_id) FROM enhanced_contracts WHERE is_active = TRUE) as networks_with_contracts;

-- View لإحصائيات العروض المفصلة
CREATE VIEW offers_stats AS
SELECT
    o.currency,
    o.offer_type,
    o.contract_status,
    COUNT(*) as offer_count,
    SUM(o.amount) as total_amount,
    AVG(o.amount) as avg_amount,
    MIN(o.amount) as min_amount,
    MAX(o.amount) as max_amount,
    AVG(o.price) as avg_price,
    COUNT(CASE WHEN o.is_active = TRUE THEN 1 END) as active_count,
    COUNT(CASE WHEN o.contract_status = 'completed' THEN 1 END) as completed_count
FROM offers o
GROUP BY o.currency, o.offer_type, o.contract_status;

-- View لإحصائيات المستخدمين المفصلة
CREATE VIEW user_trading_stats AS
SELECT
    u.id,
    u.username,
    u.wallet_address,
    u.total_trades,
    u.completed_trades,
    u.total_volume,
    u.rating,
    u.rating_count,

    -- إحصائيات العروض
    COALESCE(offer_stats.total_offers, 0) as total_offers,
    COALESCE(offer_stats.active_offers, 0) as active_offers,
    COALESCE(offer_stats.completed_offers, 0) as completed_offers,

    -- إحصائيات الصفقات كبائع
    COALESCE(seller_stats.seller_trades, 0) as seller_trades,
    COALESCE(seller_stats.seller_volume, 0) as seller_volume,

    -- إحصائيات الصفقات كمشتري
    COALESCE(buyer_stats.buyer_trades, 0) as buyer_trades,
    COALESCE(buyer_stats.buyer_volume, 0) as buyer_volume,

    -- آخر نشاط
    GREATEST(
        COALESCE(offer_stats.last_offer_date, '1970-01-01'),
        COALESCE(seller_stats.last_seller_trade, '1970-01-01'),
        COALESCE(buyer_stats.last_buyer_trade, '1970-01-01')
    ) as last_activity_date

FROM users u

LEFT JOIN (
    SELECT
        user_id,
        COUNT(*) as total_offers,
        COUNT(CASE WHEN is_active = TRUE THEN 1 END) as active_offers,
        COUNT(CASE WHEN contract_status = 'completed' THEN 1 END) as completed_offers,
        MAX(created_at) as last_offer_date
    FROM offers
    GROUP BY user_id
) offer_stats ON u.id = offer_stats.user_id

LEFT JOIN (
    SELECT
        seller_id,
        COUNT(*) as seller_trades,
        SUM(total_value) as seller_volume,
        MAX(created_at) as last_seller_trade
    FROM trades
    WHERE status = 'completed'
    GROUP BY seller_id
) seller_stats ON u.id = seller_stats.seller_id

LEFT JOIN (
    SELECT
        buyer_id,
        COUNT(*) as buyer_trades,
        SUM(total_value) as buyer_volume,
        MAX(created_at) as last_buyer_trade
    FROM trades
    WHERE status = 'completed'
    GROUP BY buyer_id
) buyer_stats ON u.id = buyer_stats.buyer_id;

-- جدول رسوم المنصة
CREATE TABLE platform_fees (
    id INT PRIMARY KEY AUTO_INCREMENT,
    trade_id INT NOT NULL,
    fee_type ENUM('platform_fee','gas_fee','penalty_fee') NOT NULL,
    amount DECIMAL(20,8) NOT NULL,
    currency VARCHAR(10) NOT NULL DEFAULT 'USDT',
    status ENUM('pending','collected','refunded') NOT NULL DEFAULT 'pending',
    collected_at TIMESTAMP NULL DEFAULT NULL,
    transaction_hash VARCHAR(255) DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (trade_id) REFERENCES trades(id) ON DELETE CASCADE,
    INDEX idx_trade_id (trade_id),
    INDEX idx_fee_type (fee_type),
    INDEX idx_status (status),
    INDEX idx_collected_at (collected_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;



-- ===================================
-- جداول إدارة التداولات والنزاعات المتقدمة
-- ===================================

-- جدول النزاعات الأساسي
CREATE TABLE disputes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    trade_id INT NOT NULL,
    reported_by INT NOT NULL, -- المستخدم الذي أبلغ عن النزاع
    dispute_type ENUM('payment_not_received', 'payment_not_sent', 'wrong_amount', 'fake_payment_proof', 'payment_delayed', 'technical_issue', 'terms_violation', 'other') NOT NULL,
    description TEXT NOT NULL,
    status ENUM('pending', 'in_review', 'resolved', 'escalated', 'closed') DEFAULT 'pending',
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    assigned_admin_id INT NULL,
    evidence_files JSON NULL, -- مسارات ملفات الأدلة
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    resolved_at TIMESTAMP NULL,

    FOREIGN KEY (trade_id) REFERENCES trades(id) ON DELETE CASCADE,
    FOREIGN KEY (reported_by) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_admin_id) REFERENCES users(id) ON DELETE SET NULL,

    INDEX idx_trade_id (trade_id),
    INDEX idx_status (status),
    INDEX idx_priority (priority),
    INDEX idx_created_at (created_at),
    INDEX idx_assigned_admin (assigned_admin_id, status)
);

-- جدول ملاحظات الإدارة للنزاعات
CREATE TABLE dispute_admin_notes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    dispute_id INT NOT NULL,
    admin_id INT NOT NULL,
    note TEXT NOT NULL,
    is_private BOOLEAN DEFAULT TRUE, -- ملاحظة خاصة للإدارة أم مرئية للأطراف
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (dispute_id) REFERENCES disputes(id) ON DELETE CASCADE,
    FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE,

    INDEX idx_dispute_id (dispute_id),
    INDEX idx_admin_id (admin_id),
    INDEX idx_created_at (created_at)
);

-- جدول رسائل الإدارة للأطراف
CREATE TABLE dispute_messages (
    id INT PRIMARY KEY AUTO_INCREMENT,
    dispute_id INT NOT NULL,
    admin_id INT NOT NULL,
    recipient_type ENUM('seller', 'buyer', 'both') NOT NULL,
    subject VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    is_urgent BOOLEAN DEFAULT FALSE,
    is_read BOOLEAN DEFAULT FALSE,
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    read_at TIMESTAMP NULL,

    FOREIGN KEY (dispute_id) REFERENCES disputes(id) ON DELETE CASCADE,
    FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE,

    INDEX idx_dispute_id (dispute_id),
    INDEX idx_admin_id (admin_id),
    INDEX idx_recipient_type (recipient_type),
    INDEX idx_sent_at (sent_at),
    INDEX idx_is_urgent (is_urgent, sent_at)
);

-- جدول قرارات النزاعات
CREATE TABLE dispute_resolutions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    dispute_id INT NOT NULL, -- مرتبط بجدول النزاعات الجديد
    trade_id INT NOT NULL,
    admin_id INT NOT NULL,
    resolution_type ENUM('seller_favor', 'buyer_favor', 'partial_refund') NOT NULL,
    reason TEXT NOT NULL, -- سبب القرار
    admin_notes TEXT NULL, -- ملاحظات إدارية إضافية
    resolution_amount DECIMAL(20,8) DEFAULT 0,
    smart_contract_tx_hash VARCHAR(66) NULL,
    resolved_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (dispute_id) REFERENCES disputes(id) ON DELETE CASCADE,
    FOREIGN KEY (trade_id) REFERENCES trades(id) ON DELETE CASCADE,
    FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE,

    INDEX idx_dispute_id (dispute_id),
    INDEX idx_trade_resolution (trade_id),
    INDEX idx_admin_resolutions (admin_id, created_at),
    INDEX idx_resolution_type (resolution_type, created_at)
);

-- جدول ملاحظات المدراء على الصفقات
CREATE TABLE admin_trade_notes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    trade_id INT NOT NULL,
    admin_id INT NOT NULL,
    note TEXT NOT NULL,
    is_internal BOOLEAN DEFAULT TRUE,
    visibility ENUM('internal', 'public', 'parties_only') DEFAULT 'internal',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (trade_id) REFERENCES trades(id) ON DELETE CASCADE,
    FOREIGN KEY (admin_id) REFERENCES users(id),
    INDEX idx_trade_notes (trade_id, created_at),
    INDEX idx_admin_notes (admin_id, created_at),
    INDEX idx_visibility (visibility, created_at)
);

-- جدول سجل نشاط المدراء
CREATE TABLE admin_activity_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    admin_id INT NOT NULL,
    action_type VARCHAR(50) NOT NULL,
    target_type VARCHAR(50) NOT NULL,
    target_id INT NOT NULL,
    details JSON NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (admin_id) REFERENCES users(id),
    INDEX idx_admin_activity (admin_id, created_at),
    INDEX idx_action_type (action_type, created_at),
    INDEX idx_target (target_type, target_id),
    INDEX idx_ip_address (ip_address, created_at)
);

-- جدول إحصائيات التداولات المتقدمة
CREATE TABLE trade_statistics (
    id INT PRIMARY KEY AUTO_INCREMENT,
    date_recorded DATE NOT NULL,
    total_trades INT DEFAULT 0,
    completed_trades INT DEFAULT 0,
    disputed_trades INT DEFAULT 0,
    cancelled_trades INT DEFAULT 0,
    total_volume DECIMAL(20,8) DEFAULT 0,
    average_completion_time INT DEFAULT 0, -- بالدقائق
    dispute_rate DECIMAL(5,2) DEFAULT 0, -- نسبة مئوية
    completion_rate DECIMAL(5,2) DEFAULT 0, -- نسبة مئوية
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    UNIQUE KEY unique_date (date_recorded),
    INDEX idx_date_recorded (date_recorded),
    INDEX idx_completion_rate (completion_rate, date_recorded)
);

-- جدول إحصائيات النزاعات
CREATE TABLE dispute_statistics (
    id INT PRIMARY KEY AUTO_INCREMENT,
    date_recorded DATE NOT NULL,
    total_disputes INT DEFAULT 0,
    pending_disputes INT DEFAULT 0,
    resolved_disputes INT DEFAULT 0,
    escalated_disputes INT DEFAULT 0,
    seller_favor_count INT DEFAULT 0,
    buyer_favor_count INT DEFAULT 0,
    partial_refund_count INT DEFAULT 0,
    average_resolution_time INT DEFAULT 0, -- بالساعات
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    UNIQUE KEY unique_date (date_recorded),
    INDEX idx_date_recorded (date_recorded),
    INDEX idx_resolution_stats (resolved_disputes, average_resolution_time)
);

-- ===================================
-- فهارس محسنة للأداء
-- ===================================

-- فهارس محسنة لجدول الصفقات
ALTER TABLE trades ADD INDEX idx_admin_management (status, created_at, updated_at);
ALTER TABLE trades ADD INDEX idx_dispute_filter (status, seller_id, buyer_id, created_at);
ALTER TABLE trades ADD INDEX idx_amount_filter (amount, currency, created_at);
ALTER TABLE trades ADD INDEX idx_completion_time (created_at, completed_at);

-- فهارس محسنة لجدول المستخدمين
ALTER TABLE users ADD INDEX idx_admin_filter (is_admin, is_active, created_at);
ALTER TABLE users ADD INDEX idx_verification_status (is_verified, is_active);
ALTER TABLE users ADD INDEX idx_trading_stats (total_trades, completed_trades, rating);

-- فهارس محسنة لجدول العروض
ALTER TABLE offers ADD INDEX idx_admin_offers (contract_status, created_at, updated_at);
ALTER TABLE offers ADD INDEX idx_currency_filter (currency, contract_status, created_at);

-- فهارس محسنة لجدول الرسائل
ALTER TABLE messages ADD INDEX idx_trade_messages (trade_id, created_at);
ALTER TABLE messages ADD INDEX idx_unread_messages (is_read, created_at);

-- ===================================
-- تحديثات إضافية للمرحلة الأولى
-- ===================================

-- إضافة فهارس مهمة
CREATE INDEX idx_messages_updated_at ON messages(created_at);
CREATE INDEX idx_notifications_updated_at ON notifications(created_at);
CREATE INDEX idx_users_admin_active ON users(is_admin, is_active);

-- إدراج إعدادات المنصة الافتراضية
INSERT IGNORE INTO platform_settings (setting_key, setting_value, setting_type, description) VALUES
('platformFee', '1.5', 'float', 'رسوم المنصة بالنسبة المئوية'),
('minTradeAmount', '10', 'integer', 'أقل مبلغ للصفقة'),
('maxTradeAmount', '50000', 'integer', 'أكبر مبلغ للصفقة'),
('defaultTradeTimeout', '1800', 'integer', 'مهلة الصفقة الافتراضية بالثواني'),
('maintenanceMode', '0', 'boolean', 'وضع الصيانة'),
('registrationEnabled', '1', 'boolean', 'تفعيل التسجيل الجديد'),
('supportedCurrencies', '["SAR", "AED", "KWD", "QAR", "BHD", "OMR", "JOD", "EGP", "USD", "EUR", "GBP", "JPY", "CAD", "AUD", "CHF", "CNY", "TRY", "LBP", "MAD", "TND", "DZD", "IQD", "SYP", "YER", "PKR", "INR", "BDT", "MYR", "IDR", "SGD"]', 'json', 'العملات المدعومة'),
('maxFileSize', '10485760', 'integer', 'أقصى حجم للملف بالبايت (10MB)'),
('allowedFileTypes', '["jpg", "jpeg", "png", "pdf", "doc", "docx"]', 'json', 'أنواع الملفات المسموحة'),
('emailVerificationRequired', '0', 'boolean', 'إجبارية التحقق من البريد الإلكتروني'),
('kycRequired', '0', 'boolean', 'إجبارية التحقق من الهوية'),
('autoApproveOffers', '1', 'boolean', 'الموافقة التلقائية على العروض'),
('disputeTimeoutHours', '72', 'integer', 'مهلة النزاع بالساعات'),
('platformName', 'IKAROS P2P', 'string', 'اسم المنصة'),
('platformVersion', '1.0.0', 'string', 'إصدار المنصة'),
('contractAddress', '******************************************', 'string', 'عنوان العقد الذكي المحسن (Core Escrow)'),
('networkChainId', '97', 'integer', 'معرف شبكة البلوك تشين (97 = BSC Testnet)'),
('rpcUrl', 'https://data-seed-prebsc-1-s1.binance.org:8545/', 'string', 'رابط RPC للشبكة'),
('blockExplorerUrl', 'https://testnet.bscscan.com', 'string', 'رابط مستكشف البلوكات'),

-- إعدادات الأمان الإضافية
('adminSessionTimeout', '8', 'integer', 'مدة جلسة المدير بالساعات'),
('maxAdminLoginAttempts', '3', 'integer', 'أقصى عدد محاولات دخول المدير'),
('adminLockoutDuration', '1800', 'integer', 'مدة حظر المدير بالثواني (30 دقيقة)'),
('requireAdminTwoFactor', '0', 'boolean', 'إجبارية المصادقة الثنائية للمدراء'),
('adminIpWhitelistEnabled', '0', 'boolean', 'تفعيل قائمة IP البيضاء للمدراء'),
('logAdminActivity', '1', 'boolean', 'تسجيل نشاط المدراء'),
('adminPasswordMinLength', '8', 'integer', 'أقل طول لكلمة مرور المدير'),
('adminPasswordComplexity', '1', 'boolean', 'تعقيد كلمة مرور المدير مطلوب'),

-- إعدادات العروض المجانية
('freeOffersEnabled', '1', 'boolean', 'تفعيل العروض المجانية'),
('maxFreeOffersPerUser', '3', 'integer', 'أقصى عدد عروض مجانية لكل مستخدم'),
('maxFreeOfferAmount', '1000', 'integer', 'أقصى مبلغ للعرض المجاني (USDT)'),
('freeOfferTimeLimit', '86400', 'integer', 'مدة صلاحية العرض المجاني بالثواني (24 ساعة)'),
('freeOfferRequireVerification', '0', 'boolean', 'إلزامية التحقق للعروض المجانية'),
('freeOfferMinAmount', '10', 'integer', 'أقل مبلغ للعرض المجاني (USDT)'),
('freeOfferCooldownPeriod', '3600', 'integer', 'فترة الانتظار بين العروض المجانية بالثواني (ساعة واحدة)');



-- جدول تتبع العروض المجانية
CREATE TABLE user_free_offers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    offer_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (offer_id) REFERENCES offers(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_offer_id (offer_id),
    INDEX idx_expires_at (expires_at),
    INDEX idx_active (is_active),
    INDEX idx_user_active (user_id, is_active)
);



-- إدراج الشبكات المدعومة
-- إدراج الشبكات المدعومة الأساسية (محدث للدعم الشامل)
INSERT INTO supported_networks (
    network_name, network_symbol, chain_id, rpc_url, explorer_url,
    is_testnet, is_active, gas_price_gwei, block_time_seconds, confirmation_blocks,
    created_at, updated_at
) VALUES
-- Ethereum Networks (غير مفعلة مؤقتاً)
('Ethereum Mainnet', 'ETH', 1, 'https://mainnet.infura.io/v3/YOUR_PROJECT_ID', 'https://etherscan.io', FALSE, FALSE, 20.0, 12, 12, NOW(), NOW()),
('Ethereum Sepolia', 'ETH', 11155111, 'https://sepolia.infura.io/v3/YOUR_PROJECT_ID', 'https://sepolia.etherscan.io', TRUE, FALSE, 2.0, 12, 12, NOW(), NOW()),

-- Binance Smart Chain Networks (BSC Testnet مفعل للعقود المنشورة)
('BSC Testnet', 'tBNB', 97, 'https://data-seed-prebsc-1-s1.binance.org:8545/', 'https://testnet.bscscan.com', TRUE, TRUE, 10.0, 3, 3, NOW(), NOW()),
('BSC Mainnet', 'BNB', 56, 'https://bsc-dataseed1.binance.org/', 'https://bscscan.com', FALSE, FALSE, 5.0, 3, 3, NOW(), NOW()),

-- Polygon Networks (غير مفعلة مؤقتاً)
('Polygon Mainnet', 'MATIC', 137, 'https://polygon-rpc.com/', 'https://polygonscan.com', FALSE, FALSE, 30.0, 2, 20, NOW(), NOW()),
('Polygon Mumbai', 'MATIC', 80001, 'https://rpc-mumbai.maticvigil.com/', 'https://mumbai.polygonscan.com', TRUE, FALSE, 1.0, 2, 20, NOW(), NOW()),

-- Arbitrum Networks (غير مفعلة مؤقتاً)
('Arbitrum One', 'ETH', 42161, 'https://arb1.arbitrum.io/rpc', 'https://arbiscan.io', FALSE, FALSE, 0.1, 1, 1, NOW(), NOW()),
('Arbitrum Sepolia', 'ETH', 421614, 'https://sepolia-rollup.arbitrum.io/rpc', 'https://sepolia.arbiscan.io', TRUE, FALSE, 0.1, 1, 1, NOW(), NOW()),

-- Optimism Networks (غير مفعلة مؤقتاً)
('Optimism Mainnet', 'ETH', 10, 'https://mainnet.optimism.io', 'https://optimistic.etherscan.io', FALSE, FALSE, 0.001, 2, 1, NOW(), NOW()),
('Optimism Sepolia', 'ETH', 11155420, 'https://sepolia.optimism.io', 'https://sepolia-optimism.etherscan.io', TRUE, FALSE, 0.001, 2, 1, NOW(), NOW()),

-- Avalanche Networks (غير مفعلة مؤقتاً)
('Avalanche C-Chain', 'AVAX', 43114, 'https://api.avax.network/ext/bc/C/rpc', 'https://snowtrace.io', FALSE, FALSE, 25.0, 2, 1, NOW(), NOW()),
('Avalanche Fuji', 'AVAX', 43113, 'https://api.avax-test.network/ext/bc/C/rpc', 'https://testnet.snowtrace.io', TRUE, FALSE, 25.0, 2, 1, NOW(), NOW())

ON DUPLICATE KEY UPDATE
    network_name = VALUES(network_name),
    network_symbol = VALUES(network_symbol),
    rpc_url = VALUES(rpc_url),
    explorer_url = VALUES(explorer_url),
    is_testnet = VALUES(is_testnet),
    is_active = VALUES(is_active),
    gas_price_gwei = VALUES(gas_price_gwei),
    block_time_seconds = VALUES(block_time_seconds),
    confirmation_blocks = VALUES(confirmation_blocks),
    updated_at = NOW();

-- ملاحظة: تم حذف البيانات الأولية للعملات
-- سيتم إضافة العملات تلقائياً عبر نظام إدارة العقود الذكية الجديد
-- يمكن للمدير إضافة العملات يدوياً أو عبر التحقق من العقود الذكية

-- ملاحظة: العملات للشبكة الرئيسية (BSC Mainnet) مدرجة أعلاه مع الشبكة التجريبية

-- إدراج عناوين العقود المحسنة لشبكة BSC Testnet (المنشورة والمفعلة)
-- ملاحظة: network_id = 3 يشير إلى BSC Testnet (الشبكة الثالثة في القائمة)
INSERT INTO enhanced_contracts (network_id, contract_type, contract_address, contract_version, deployment_block, is_active, created_at, updated_at) VALUES
(3, 'core_escrow', '******************************************', '2.0.0', NULL, TRUE, NOW(), NOW()),
(3, 'reputation_manager', '0x56A6914523413b0e7344f57466A6239fCC97b913', '2.0.0', NULL, TRUE, NOW(), NOW()),
(3, 'oracle_manager', '0xB70715392F62628Ccd1258AAF691384bE8C023b6', '2.0.0', NULL, TRUE, NOW(), NOW()),
(3, 'admin_manager', '0x5A9FD8082ADA38678721D59AAB4d4F76883c5575', '2.0.0', NULL, TRUE, NOW(), NOW()),
(3, 'escrow_integrator', '******************************************', '2.0.0', NULL, TRUE, NOW(), NOW())

ON DUPLICATE KEY UPDATE
    contract_address = VALUES(contract_address),
    contract_version = VALUES(contract_version),
    is_active = VALUES(is_active),
    updated_at = NOW();

-- إدراج العملات المدعومة لشبكة BSC Testnet
INSERT INTO supported_tokens (
    network_id, token_address, token_symbol, token_name, decimals,
    is_stablecoin, is_active, icon_url, coingecko_id, created_at, updated_at
) VALUES
-- العملات المستقرة الرئيسية على BSC Testnet (network_id = 3)
(3, '******************************************', 'USDT', 'Tether USD (BSC Testnet)', 18, TRUE, TRUE, 'https://cryptologos.cc/logos/tether-usdt-logo.png', 'tether', NOW(), NOW()),
(3, '******************************************', 'USDC', 'USD Coin (BSC Testnet)', 18, TRUE, TRUE, 'https://cryptologos.cc/logos/usd-coin-usdc-logo.png', 'usd-coin', NOW(), NOW()),
(3, '******************************************', 'BUSD', 'Binance USD (BSC Testnet)', 18, TRUE, TRUE, 'https://cryptologos.cc/logos/binance-usd-busd-logo.png', 'binance-usd', NOW(), NOW()),
(3, '******************************************', 'DAI', 'Dai Stablecoin (BSC Testnet)', 18, TRUE, TRUE, 'https://cryptologos.cc/logos/multi-collateral-dai-dai-logo.png', 'dai', NOW(), NOW())

ON DUPLICATE KEY UPDATE
    token_address = VALUES(token_address),
    token_name = VALUES(token_name),
    decimals = VALUES(decimals),
    is_stablecoin = VALUES(is_stablecoin),
    is_active = VALUES(is_active),
    icon_url = VALUES(icon_url),
    coingecko_id = VALUES(coingecko_id),
    updated_at = NOW();

-- إدراج أسعار صرف العملات الافتراضية
INSERT INTO fiat_exchange_rates (base_currency, target_currency, exchange_rate, inverse_rate, source, is_active) VALUES
('USD', 'SAR', 3.7500, 0.26666667, 'manual', TRUE),
('USD', 'AED', 3.6725, 0.27233115, 'manual', TRUE),
('USD', 'KWD', 0.3070, 3.25732899, 'manual', TRUE),
('USD', 'QAR', 3.6400, 0.27472527, 'manual', TRUE),
('USD', 'BHD', 0.3760, 2.65957447, 'manual', TRUE),
('USD', 'OMR', 0.3845, 2.60078023, 'manual', TRUE),
('USD', 'JOD', 0.7090, 1.41043725, 'manual', TRUE),
('USD', 'EGP', 30.9000, 0.03236246, 'manual', TRUE),
('USD', 'EUR', 0.9200, 1.08695652, 'manual', TRUE),
('USD', 'GBP', 0.7900, 1.26582278, 'manual', TRUE);

-- إدراج أسعار العملات المستقرة الافتراضية
INSERT INTO oracle_price_feeds (network_id, token_id, currency, price, confidence_level, is_active) VALUES
-- USDT prices (network_id = 3 for BSC Testnet, token_id = 1 for USDT)
(3, 1, 'USD', 1.00000000, 9900, TRUE),
(3, 1, 'SAR', 3.75000000, 9500, TRUE),
(3, 1, 'AED', 3.67250000, 9500, TRUE),
(3, 1, 'KWD', 0.30700000, 9500, TRUE),
(3, 1, 'QAR', 3.64000000, 9500, TRUE),
(3, 1, 'BHD', 0.37600000, 9500, TRUE),
(3, 1, 'OMR', 0.38450000, 9500, TRUE),
(3, 1, 'JOD', 0.70900000, 9500, TRUE),
(3, 1, 'EGP', 30.90000000, 9500, TRUE),

-- USDC prices (network_id = 3 for BSC Testnet, token_id = 2 for USDC)
(3, 2, 'USD', 1.00000000, 9900, TRUE),
(3, 2, 'SAR', 3.75000000, 9500, TRUE),
(3, 2, 'AED', 3.67250000, 9500, TRUE),
(3, 2, 'KWD', 0.30700000, 9500, TRUE),
(3, 2, 'QAR', 3.64000000, 9500, TRUE),

-- BUSD prices (network_id = 3 for BSC Testnet, token_id = 3 for BUSD)
(3, 3, 'USD', 1.00000000, 9900, TRUE),
(3, 3, 'SAR', 3.75000000, 9500, TRUE),
(3, 3, 'AED', 3.67250000, 9500, TRUE),

-- DAI prices (network_id = 3 for BSC Testnet, token_id = 4 for DAI)
(3, 4, 'USD', 1.00000000, 9800, TRUE),
(3, 4, 'SAR', 3.75000000, 9400, TRUE);

-- إضافة إعدادات جديدة للنظام
INSERT INTO platform_settings (setting_key, setting_value, setting_type, description) VALUES
('monthly_free_offers_default', '3', 'integer', 'عدد العروض المجانية الافتراضية شهرياً'),
('subscription_system_enabled', 'true', 'boolean', 'تفعيل نظام الاشتراكات'),
('free_plan_id', '1', 'integer', 'معرف الخطة المجانية الافتراضية'),
('enhanced_contracts_enabled', 'true', 'boolean', 'تفعيل العقود المحسنة'),
('multi_token_support', 'true', 'boolean', 'دعم العملات المتعددة'),
('default_network_id', '1', 'integer', 'معرف الشبكة الافتراضية (1=BSC Testnet)'),
('platform_fee_rate', '0.0050', 'decimal', 'معدل رسوم المنصة (0.5%)'),
('min_reputation_score', '0', 'integer', 'الحد الأدنى لنقاط السمعة'),
('max_reputation_score', '1000', 'integer', 'الحد الأقصى لنقاط السمعة'),
('initial_reputation_score', '100', 'integer', 'نقاط السمعة الأولية للمستخدمين الجدد'),

-- إعدادات العقود المحسنة الإضافية
('contract_verification_enabled', 'true', 'boolean', 'تفعيل التحقق من العقود'),
('auto_token_discovery', 'true', 'boolean', 'الاكتشاف التلقائي للعملات'),
('blockchain_sync_enabled', 'true', 'boolean', 'تفعيل مزامنة البلوك تشين'),
('bscscan_api_key', '', 'string', 'مفتاح BSCScan API'),
('etherscan_api_key', '', 'string', 'مفتاح Etherscan API'),
('contract_analysis_timeout', '30', 'integer', 'مهلة تحليل العقود بالثواني'),
('max_supported_tokens_per_network', '50', 'integer', 'أقصى عدد عملات مدعومة لكل شبكة');

-- جدول سجلات الإدارة المتقدمة
CREATE TABLE admin_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    admin_id INT NOT NULL,
    action_type ENUM('user_management', 'trade_management', 'dispute_resolution', 'system_config', 'security', 'monitoring', 'testing', 'backup') NOT NULL,
    action VARCHAR(100) NOT NULL,
    target_type ENUM('user', 'trade', 'dispute', 'contract', 'network', 'token', 'setting', 'notification', 'system') NULL,
    target_id VARCHAR(100) NULL,
    details JSON NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    status ENUM('success', 'failed', 'pending') DEFAULT 'success',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_admin_id (admin_id),
    INDEX idx_action_type (action_type),
    INDEX idx_target_type (target_type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- جدول الإشعارات المتقدمة
CREATE TABLE admin_notifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    type ENUM('system', 'security', 'trade', 'dispute', 'user', 'network', 'contract', 'performance') NOT NULL,
    priority ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    data JSON NULL,
    channels JSON NULL, -- ['email', 'sms', 'push', 'webhook', 'slack']
    target_admins JSON NULL, -- admin IDs or 'all'
    read_by JSON NULL, -- admin IDs who read this notification
    acknowledged_by JSON NULL, -- admin IDs who acknowledged this notification
    is_active BOOLEAN DEFAULT TRUE,
    expires_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_type (type),
    INDEX idx_priority (priority),
    INDEX idx_is_active (is_active),
    INDEX idx_created_at (created_at),
    INDEX idx_expires_at (expires_at)
);

-- جدول مراقبة النظام
CREATE TABLE system_monitoring (
    id INT PRIMARY KEY AUTO_INCREMENT,
    metric_type ENUM('cpu', 'memory', 'disk', 'network', 'database', 'api', 'blockchain', 'cache') NOT NULL,
    metric_name VARCHAR(100) NOT NULL,
    value DECIMAL(15, 6) NOT NULL,
    unit VARCHAR(20) NOT NULL,
    threshold_warning DECIMAL(15, 6) NULL,
    threshold_critical DECIMAL(15, 6) NULL,
    status ENUM('normal', 'warning', 'critical') DEFAULT 'normal',
    metadata JSON NULL,
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_metric_type (metric_type),
    INDEX idx_metric_name (metric_name),
    INDEX idx_status (status),
    INDEX idx_recorded_at (recorded_at)
);

-- جدول حالة الخدمات
CREATE TABLE service_health (
    id INT PRIMARY KEY AUTO_INCREMENT,
    service_name VARCHAR(100) NOT NULL,
    service_type ENUM('database', 'api', 'blockchain', 'cache', 'storage', 'external') NOT NULL,
    status ENUM('online', 'offline', 'degraded', 'maintenance') NOT NULL,
    response_time INT NULL, -- milliseconds
    last_check TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    error_message TEXT NULL,
    metadata JSON NULL,
    uptime_percentage DECIMAL(5, 2) DEFAULT 100.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    UNIQUE KEY unique_service (service_name),
    INDEX idx_service_type (service_type),
    INDEX idx_status (status),
    INDEX idx_last_check (last_check)
);

-- جدول اختبارات النظام
CREATE TABLE system_tests (
    id INT PRIMARY KEY AUTO_INCREMENT,
    test_suite VARCHAR(100) NOT NULL,
    test_name VARCHAR(200) NOT NULL,
    test_type ENUM('contract', 'simulation', 'network', 'system', 'load', 'security', 'api', 'performance') NOT NULL,
    status ENUM('pending', 'running', 'completed', 'failed', 'paused', 'cancelled') DEFAULT 'pending',
    result ENUM('passed', 'failed', 'warning', 'skipped', 'timeout', 'error') NULL,
    started_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    duration INT NULL, -- milliseconds
    progress INT DEFAULT 0, -- percentage
    config JSON NULL,
    logs JSON NULL,
    metrics JSON NULL,
    error_details TEXT NULL,
    created_by INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_test_suite (test_suite),
    INDEX idx_test_type (test_type),
    INDEX idx_status (status),
    INDEX idx_result (result),
    INDEX idx_created_by (created_by),
    INDEX idx_created_at (created_at)
);

-- جدول إعدادات النظام المتقدمة
CREATE TABLE advanced_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    category ENUM('platform', 'fees', 'security', 'api', 'blockchain', 'maintenance', 'backup', 'monitoring', 'testing') NOT NULL,
    setting_key VARCHAR(100) NOT NULL,
    setting_value TEXT NOT NULL,
    data_type ENUM('string', 'number', 'boolean', 'json', 'array') DEFAULT 'string',
    is_sensitive BOOLEAN DEFAULT FALSE,
    is_readonly BOOLEAN DEFAULT FALSE,
    validation_rules JSON NULL,
    description TEXT NULL,
    last_modified_by INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    UNIQUE KEY unique_setting (category, setting_key),
    FOREIGN KEY (last_modified_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_category (category),
    INDEX idx_setting_key (setting_key),
    INDEX idx_is_sensitive (is_sensitive),
    INDEX idx_last_modified_by (last_modified_by)
);

-- جدول النسخ الاحتياطية
CREATE TABLE system_backups (
    id INT PRIMARY KEY AUTO_INCREMENT,
    backup_type ENUM('full', 'incremental', 'differential', 'manual') NOT NULL,
    backup_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL, -- bytes
    compression_type ENUM('none', 'gzip', 'zip', 'tar') DEFAULT 'gzip',
    encryption_enabled BOOLEAN DEFAULT FALSE,
    checksum VARCHAR(64) NULL,
    status ENUM('pending', 'in_progress', 'completed', 'failed', 'corrupted') DEFAULT 'pending',
    started_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    expires_at TIMESTAMP NULL,
    created_by INT NULL,
    metadata JSON NULL,
    error_message TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_backup_type (backup_type),
    INDEX idx_status (status),
    INDEX idx_created_by (created_by),
    INDEX idx_created_at (created_at),
    INDEX idx_expires_at (expires_at)
);

-- إدراج بيانات أولية للإعدادات المتقدمة
INSERT INTO advanced_settings (category, setting_key, setting_value, data_type, description) VALUES
-- إعدادات المنصة
('platform', 'site_name', 'Ikaros P2P', 'string', 'اسم المنصة'),
('platform', 'site_description', 'Secure P2P Trading Platform', 'string', 'وصف المنصة'),
('platform', 'default_language', 'ar', 'string', 'اللغة الافتراضية'),
('platform', 'default_currency', 'USD', 'string', 'العملة الافتراضية'),
('platform', 'timezone', 'Asia/Riyadh', 'string', 'المنطقة الزمنية'),
('platform', 'registration_enabled', 'true', 'boolean', 'تفعيل التسجيل'),
('platform', 'kyc_required', 'true', 'boolean', 'التحقق من الهوية مطلوب'),
('platform', 'max_users_per_day', '100', 'number', 'الحد الأقصى للمستخدمين يومياً'),
('platform', 'session_timeout', '3600', 'number', 'انتهاء صلاحية الجلسة بالثواني'),

-- إعدادات الرسوم
('fees', 'trading_fee', '0.5', 'number', 'رسوم التداول بالنسبة المئوية'),
('fees', 'withdrawal_fee', '0.1', 'number', 'رسوم السحب بالنسبة المئوية'),
('fees', 'deposit_fee', '0.0', 'number', 'رسوم الإيداع بالنسبة المئوية'),
('fees', 'dispute_fee', '1.0', 'number', 'رسوم النزاعات بالنسبة المئوية'),
('fees', 'minimum_trade_amount', '10', 'number', 'الحد الأدنى للتداول بالدولار'),
('fees', 'maximum_trade_amount', '100000', 'number', 'الحد الأقصى للتداول بالدولار'),

-- إعدادات الأمان
('security', 'two_factor_required', 'true', 'boolean', 'المصادقة الثنائية مطلوبة للإدارة'),
('security', 'max_login_attempts', '5', 'number', 'الحد الأقصى لمحاولات تسجيل الدخول'),
('security', 'password_min_length', '8', 'number', 'الحد الأدنى لطول كلمة المرور'),
('security', 'session_security_enabled', 'true', 'boolean', 'تفعيل أمان الجلسة'),
('security', 'ip_whitelist_enabled', 'false', 'boolean', 'تفعيل القائمة البيضاء للـ IP'),

-- إعدادات API
('api', 'rate_limit_per_hour', '1000', 'number', 'حد الطلبات في الساعة'),
('api', 'api_versioning_enabled', 'true', 'boolean', 'تفعيل إصدارات API'),
('api', 'cors_enabled', 'true', 'boolean', 'تفعيل CORS'),
('api', 'api_documentation_enabled', 'true', 'boolean', 'تفعيل وثائق API'),

-- إعدادات البلوك تشين
('blockchain', 'ethereum_rpc_url', 'https://mainnet.infura.io/v3/YOUR_PROJECT_ID', 'string', 'رابط RPC لشبكة Ethereum'),
('blockchain', 'bsc_rpc_url', 'https://bsc-dataseed.binance.org/', 'string', 'رابط RPC لشبكة BSC'),
('blockchain', 'polygon_rpc_url', 'https://polygon-rpc.com/', 'string', 'رابط RPC لشبكة Polygon'),
('blockchain', 'gas_limit', '21000', 'number', 'حد الغاز الافتراضي'),
('blockchain', 'confirmations_required', '12', 'number', 'عدد التأكيدات المطلوبة'),
('blockchain', 'network_timeout', '30000', 'number', 'مهلة انتظار الشبكة بالميلي ثانية'),

-- إعدادات الصيانة
('maintenance', 'maintenance_mode', 'false', 'boolean', 'وضع الصيانة'),
('maintenance', 'maintenance_message', 'النظام قيد الصيانة، يرجى المحاولة لاحقاً', 'string', 'رسالة الصيانة'),
('maintenance', 'auto_backup_enabled', 'true', 'boolean', 'تفعيل النسخ الاحتياطي التلقائي'),
('maintenance', 'backup_frequency', 'daily', 'string', 'تكرار النسخ الاحتياطي'),
('maintenance', 'log_retention_days', '30', 'number', 'مدة الاحتفاظ بالسجلات بالأيام'),

-- إعدادات النسخ الاحتياطي
('backup', 'backup_location', '/backups/', 'string', 'مجلد النسخ الاحتياطية'),
('backup', 'backup_encryption', 'true', 'boolean', 'تشفير النسخ الاحتياطية'),
('backup', 'backup_retention_days', '90', 'number', 'مدة الاحتفاظ بالنسخ الاحتياطية'),
('backup', 'cloud_backup_enabled', 'false', 'boolean', 'تفعيل النسخ الاحتياطي السحابي'),

-- إعدادات المراقبة
('monitoring', 'monitoring_enabled', 'true', 'boolean', 'تفعيل مراقبة النظام'),
('monitoring', 'alert_threshold_cpu', '80', 'number', 'حد تنبيه استخدام المعالج'),
('monitoring', 'alert_threshold_memory', '85', 'number', 'حد تنبيه استخدام الذاكرة'),
('monitoring', 'alert_threshold_disk', '90', 'number', 'حد تنبيه استخدام القرص'),
('monitoring', 'monitoring_interval', '60', 'number', 'فترة المراقبة بالثواني'),

-- إعدادات الاختبار
('testing', 'testing_environment', 'staging', 'string', 'بيئة الاختبار'),
('testing', 'parallel_execution', '3', 'number', 'عدد الاختبارات المتوازية'),
('testing', 'test_timeout', '60', 'number', 'مهلة انتظار الاختبار بالثواني'),
('testing', 'stop_on_failure', 'true', 'boolean', 'إيقاف الاختبارات عند الفشل'),
('testing', 'detailed_logs', 'true', 'boolean', 'تفعيل السجلات المفصلة');

-- إدراج بيانات أولية لحالة الخدمات
INSERT INTO service_health (service_name, service_type, status, response_time, uptime_percentage) VALUES
('MySQL Database', 'database', 'online', 5, 99.99),
('Redis Cache', 'cache', 'online', 2, 99.95),
('API Server', 'api', 'online', 150, 99.90),
('Ethereum Node', 'blockchain', 'online', 300, 99.80),
('BSC Node', 'blockchain', 'online', 250, 99.85),
('File Storage', 'storage', 'online', 100, 99.95),
('Email Service', 'external', 'online', 500, 99.50),
('SMS Service', 'external', 'online', 800, 99.20);

-- إدراج إشعارات نظام أولية
INSERT INTO admin_notifications (type, priority, title, message, channels, target_admins, is_active) VALUES
('system', 'medium', 'مرحباً بك في لوحة التحكم المتقدمة', 'تم تفعيل النظام الإداري المتقدم بنجاح مع جميع الميزات الجديدة', '["push"]', '"all"', TRUE),
('security', 'high', 'تحديث أمني مهم', 'يرجى مراجعة إعدادات الأمان والتأكد من تفعيل المصادقة الثنائية', '["email", "push"]', '"all"', TRUE),
('system', 'low', 'نسخة احتياطية تلقائية', 'تم إنشاء نسخة احتياطية تلقائية بنجاح', '["push"]', '"all"', TRUE);

-- =====================================================
-- نظام الباقات والاشتراكات المدفوعة
-- =====================================================

-- جدول خطط الاشتراك
CREATE TABLE subscription_plans (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(50) UNIQUE NOT NULL,
    type ENUM('free', 'basic', 'pro', 'enterprise') NOT NULL,
    price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    billing_cycle ENUM('monthly', 'yearly') NOT NULL DEFAULT 'monthly',

    -- ميزات الخطة
    offers_limit INT NOT NULL DEFAULT 3, -- -1 للا محدود
    commission_rate DECIMAL(5,2) NOT NULL DEFAULT 2.00, -- نسبة العمولة
    support_level ENUM('basic', 'advanced', 'dedicated', '24/7') NOT NULL DEFAULT 'basic',
    analytics_access BOOLEAN NOT NULL DEFAULT FALSE,
    priority_level INT NOT NULL DEFAULT 1, -- 1-4 (أعلى رقم = أولوية أعلى)
    api_access BOOLEAN NOT NULL DEFAULT FALSE,
    escrow_free BOOLEAN NOT NULL DEFAULT FALSE,
    white_label BOOLEAN NOT NULL DEFAULT FALSE,
    dedicated_manager BOOLEAN NOT NULL DEFAULT FALSE,

    -- إعدادات الخطة
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    is_featured BOOLEAN NOT NULL DEFAULT FALSE,
    sort_order INT NOT NULL DEFAULT 0,

    -- معلومات إضافية
    description TEXT,
    features JSON, -- ميزات إضافية بصيغة JSON
    limitations JSON, -- قيود الخطة

    -- تواريخ
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_type (type),
    INDEX idx_active (is_active),
    INDEX idx_price (price)
);

-- جدول اشتراكات المستخدمين
CREATE TABLE user_subscriptions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    plan_id INT NOT NULL,

    -- معلومات الاشتراك
    status ENUM('active', 'expired', 'cancelled', 'pending', 'suspended') NOT NULL DEFAULT 'pending',
    start_date TIMESTAMP NOT NULL,
    end_date TIMESTAMP NOT NULL,
    auto_renew BOOLEAN NOT NULL DEFAULT TRUE,

    -- معلومات الدفع
    payment_method VARCHAR(50),
    total_paid DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',

    -- استخدام الخطة
    offers_used INT NOT NULL DEFAULT 0,
    offers_reset_date TIMESTAMP, -- تاريخ إعادة تعيين العروض

    -- معلومات التحقق (للخطة المجانية)
    is_verified BOOLEAN NOT NULL DEFAULT FALSE,
    verification_level INT NOT NULL DEFAULT 0, -- 0-5
    wallet_verified BOOLEAN NOT NULL DEFAULT FALSE,
    email_verified BOOLEAN NOT NULL DEFAULT FALSE,
    phone_verified BOOLEAN NOT NULL DEFAULT FALSE,
    identity_verified BOOLEAN NOT NULL DEFAULT FALSE,

    -- معلومات إضافية
    notes TEXT,
    metadata JSON,

    -- تواريخ
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    cancelled_at TIMESTAMP NULL,
    suspended_at TIMESTAMP NULL,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (plan_id) REFERENCES subscription_plans(id) ON DELETE RESTRICT,

    INDEX idx_user_id (user_id),
    INDEX idx_plan_id (plan_id),
    INDEX idx_status (status),
    INDEX idx_dates (start_date, end_date),
    INDEX idx_verification (is_verified, verification_level)
);

-- جدول معاملات الدفع
CREATE TABLE payment_transactions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    subscription_id INT,
    plan_id INT NOT NULL,

    -- معلومات المعاملة
    transaction_id VARCHAR(100) UNIQUE NOT NULL,
    external_transaction_id VARCHAR(100), -- معرف المعاملة الخارجي
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',

    -- حالة المعاملة
    status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded') NOT NULL DEFAULT 'pending',
    payment_method ENUM('credit_card', 'debit_card', 'paypal', 'stripe', 'crypto', 'bank_transfer') NOT NULL,
    payment_gateway VARCHAR(50),

    -- تفاصيل الدفع
    gateway_response JSON, -- استجابة بوابة الدفع
    failure_reason TEXT,
    refund_reason TEXT,
    refund_amount DECIMAL(10,2),

    -- تواريخ
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    failed_at TIMESTAMP NULL,
    refunded_at TIMESTAMP NULL,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (subscription_id) REFERENCES user_subscriptions(id) ON DELETE SET NULL,
    FOREIGN KEY (plan_id) REFERENCES subscription_plans(id) ON DELETE RESTRICT,

    INDEX idx_user_id (user_id),
    INDEX idx_subscription_id (subscription_id),
    INDEX idx_transaction_id (transaction_id),
    INDEX idx_status (status),
    INDEX idx_payment_method (payment_method),
    INDEX idx_created_at (created_at)
);

-- جدول فواتير الاشتراكات
CREATE TABLE subscription_invoices (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    subscription_id INT NOT NULL,
    transaction_id INT,

    -- معلومات الفاتورة
    invoice_number VARCHAR(50) UNIQUE NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    tax_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    total_amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',

    -- حالة الفاتورة
    status ENUM('draft', 'sent', 'paid', 'overdue', 'cancelled') NOT NULL DEFAULT 'draft',
    due_date TIMESTAMP NOT NULL,

    -- تفاصيل الفاتورة
    billing_address JSON,
    line_items JSON, -- عناصر الفاتورة
    notes TEXT,

    -- تواريخ
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    sent_at TIMESTAMP NULL,
    paid_at TIMESTAMP NULL,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (subscription_id) REFERENCES user_subscriptions(id) ON DELETE CASCADE,
    FOREIGN KEY (transaction_id) REFERENCES payment_transactions(id) ON DELETE SET NULL,

    INDEX idx_user_id (user_id),
    INDEX idx_subscription_id (subscription_id),
    INDEX idx_invoice_number (invoice_number),
    INDEX idx_status (status),
    INDEX idx_due_date (due_date)
);

-- جدول كوبونات الخصم
CREATE TABLE subscription_coupons (
    id INT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,

    -- نوع الخصم
    discount_type ENUM('percentage', 'fixed_amount') NOT NULL,
    discount_value DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',

    -- قيود الاستخدام
    usage_limit INT, -- NULL للا محدود
    usage_count INT NOT NULL DEFAULT 0,
    user_limit INT DEFAULT 1, -- عدد مرات الاستخدام لكل مستخدم

    -- قيود الخطط
    applicable_plans JSON, -- معرفات الخطط المطبقة عليها
    minimum_amount DECIMAL(10,2), -- الحد الأدنى للمبلغ

    -- صلاحية الكوبون
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    starts_at TIMESTAMP,
    expires_at TIMESTAMP,

    -- معلومات إضافية
    description TEXT,

    -- تواريخ
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_code (code),
    INDEX idx_active (is_active),
    INDEX idx_expires_at (expires_at)
);

-- جدول استخدام الكوبونات
CREATE TABLE coupon_usage (
    id INT PRIMARY KEY AUTO_INCREMENT,
    coupon_id INT NOT NULL,
    user_id INT NOT NULL,
    transaction_id INT,

    -- تفاصيل الاستخدام
    discount_amount DECIMAL(10,2) NOT NULL,
    original_amount DECIMAL(10,2) NOT NULL,
    final_amount DECIMAL(10,2) NOT NULL,

    -- تاريخ الاستخدام
    used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (coupon_id) REFERENCES subscription_coupons(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (transaction_id) REFERENCES payment_transactions(id) ON DELETE SET NULL,

    INDEX idx_coupon_id (coupon_id),
    INDEX idx_user_id (user_id),
    INDEX idx_used_at (used_at),
    UNIQUE KEY unique_user_coupon (coupon_id, user_id, transaction_id)
);

-- إدراج خطط الاشتراك الأولية
INSERT INTO subscription_plans (name, slug, type, price, currency, billing_cycle, offers_limit, commission_rate, support_level, analytics_access, priority_level, api_access, escrow_free, white_label, dedicated_manager, is_active, is_featured, description, features) VALUES
('الخطة المجانية', 'free', 'free', 0.00, 'USD', 'monthly', 3, 2.00, 'basic', FALSE, 1, FALSE, FALSE, FALSE, FALSE, TRUE, FALSE, 'خطة أساسية للمستخدمين الجدد مع 3 عروض شهرياً', '{"verification_required": true, "wallet_verification": true, "email_verification": true, "password_security": true}'),

('الخطة الأساسية', 'basic', 'basic', 9.99, 'USD', 'monthly', 15, 1.50, 'advanced', TRUE, 2, FALSE, FALSE, FALSE, FALSE, TRUE, FALSE, 'خطة مناسبة للمتداولين المبتدئين', '{"priority_matching": true, "basic_analytics": true, "advanced_support": true}'),

('الخطة الاحترافية', 'pro', 'pro', 24.99, 'USD', 'monthly', 50, 1.00, 'dedicated', TRUE, 3, TRUE, TRUE, FALSE, FALSE, TRUE, TRUE, 'خطة متقدمة للمتداولين النشطين', '{"high_priority": true, "advanced_analytics": true, "api_access": true, "free_escrow": true, "dedicated_support": true}'),

('خطة المؤسسات', 'enterprise', 'enterprise', 99.99, 'USD', 'monthly', -1, 0.50, '24/7', TRUE, 4, TRUE, TRUE, TRUE, TRUE, TRUE, FALSE, 'خطة شاملة للمؤسسات والمتداولين الكبار', '{"unlimited_offers": true, "lowest_commission": true, "24_7_support": true, "white_label": true, "dedicated_manager": true, "full_api": true, "priority_support": true}');

-- إدراج كوبونات خصم أولية
INSERT INTO subscription_coupons (code, name, discount_type, discount_value, currency, usage_limit, user_limit, applicable_plans, minimum_amount, is_active, starts_at, expires_at, description) VALUES
('WELCOME20', 'خصم الترحيب 20%', 'percentage', 20.00, 'USD', 100, 1, '[2,3,4]', 5.00, TRUE, NOW(), DATE_ADD(NOW(), INTERVAL 3 MONTH), 'خصم 20% للمستخدمين الجدد على الخطط المدفوعة'),

('UPGRADE50', 'خصم الترقية 50%', 'percentage', 50.00, 'USD', 50, 1, '[3,4]', 20.00, TRUE, NOW(), DATE_ADD(NOW(), INTERVAL 1 MONTH), 'خصم 50% عند الترقية للخطة الاحترافية أو المؤسسات'),

('SAVE10', 'وفر 10 دولار', 'fixed_amount', 10.00, 'USD', 200, 1, '[2,3,4]', 15.00, TRUE, NOW(), DATE_ADD(NOW(), INTERVAL 6 MONTH), 'خصم ثابت 10 دولار على جميع الخطط المدفوعة');

-- إدراج بيانات تجريبية للاشتراكات (اختيارية للاختبار)
-- يمكن حذف هذا القسم في الإنتاج
INSERT INTO user_subscriptions (user_id, plan_id, status, start_date, end_date, auto_renew, payment_method, total_paid, currency, offers_used, is_verified, verification_level, wallet_verified, email_verified) VALUES
(1, 1, 'active', NOW(), DATE_ADD(NOW(), INTERVAL 1 MONTH), TRUE, 'free', 0.00, 'USD', 1, TRUE, 3, TRUE, TRUE),
(2, 2, 'active', NOW(), DATE_ADD(NOW(), INTERVAL 1 MONTH), TRUE, 'credit_card', 9.99, 'USD', 8, TRUE, 4, TRUE, TRUE),
(3, 3, 'active', NOW(), DATE_ADD(NOW(), INTERVAL 1 MONTH), TRUE, 'crypto', 24.99, 'USD', 32, TRUE, 5, TRUE, TRUE);

-- إدراج معاملات دفع تجريبية
INSERT INTO payment_transactions (user_id, subscription_id, plan_id, transaction_id, external_transaction_id, amount, currency, status, payment_method, payment_gateway, processed_at, completed_at) VALUES
(2, 2, 2, 'TXN_001_BASIC', 'stripe_pi_1234567890', 9.99, 'USD', 'completed', 'credit_card', 'stripe', NOW(), NOW()),
(3, 3, 3, 'TXN_002_PRO', 'crypto_0x1234567890abcdef', 24.99, 'USD', 'completed', 'crypto', 'coinbase', NOW(), NOW());

-- ===================================================================
-- إدراج بيانات أولية للشبكات والعملات المدعومة
-- ===================================================================
-- ملاحظة: تم حذف البيانات الأولية للشبكات والعملات
-- سيتم إضافة الشبكات والعملات عبر نظام إدارة العقود الذكية الجديد
-- ===================================================================



-- إعادة تفعيل فحص المفاتيح الخارجية
SET FOREIGN_KEY_CHECKS = 1;
