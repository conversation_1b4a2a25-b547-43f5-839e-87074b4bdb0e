import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || '';
    const networkId = searchParams.get('network_id') || '';

    // بناء URL للـ PHP API
    const phpApiUrl = new URL('http://localhost/ikaros-p2p/api/enhanced-contracts/contract-testing.php');
    if (action) phpApiUrl.searchParams.set('action', action);
    if (networkId) phpApiUrl.searchParams.set('network_id', networkId);

    console.log('🔄 Proxying contract testing GET request to:', phpApiUrl.toString());

    // إرسال الطلب إلى PHP API
    const response = await fetch(phpApiUrl.toString(), {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        // نسخ headers المهمة
        'Authorization': request.headers.get('Authorization') || '',
        'Cookie': request.headers.get('Cookie') || '',
      },
    });

    const data = await response.json();
    
    console.log('📥 Contract testing GET response:', {
      status: response.status,
      action,
      success: data.success
    });

    return NextResponse.json(data, { status: response.status });

  } catch (error) {
    console.error('❌ Contract testing GET proxy error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || '';

    // بناء URL للـ PHP API
    const phpApiUrl = new URL('http://localhost/ikaros-p2p/api/enhanced-contracts/contract-testing.php');
    if (action) phpApiUrl.searchParams.set('action', action);

    console.log('🔄 Proxying contract testing POST request to:', phpApiUrl.toString());
    console.log('📤 Request body:', body);

    // إرسال الطلب إلى PHP API
    const response = await fetch(phpApiUrl.toString(), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        // نسخ headers المهمة
        'Authorization': request.headers.get('Authorization') || '',
        'Cookie': request.headers.get('Cookie') || '',
      },
      body: JSON.stringify(body),
    });

    const data = await response.json();
    
    console.log('📥 Contract testing POST response:', {
      status: response.status,
      action,
      success: data.success
    });

    return NextResponse.json(data, { status: response.status });

  } catch (error) {
    console.error('❌ Contract testing POST proxy error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
