/**
 * Hook لإدارة العملات
 * Tokens management hook
 */

import { useState, useCallback } from 'react';
import { Token, TokenStats, DefaultTokenData } from '../types';

export const useTokens = () => {
  const [tokens, setTokens] = useState<Token[]>([]);
  const [pendingTokens, setPendingTokens] = useState<Token[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // إحصائيات العملات
  const getTokenStats = useCallback((tokenList: Token[]): TokenStats => {
    return {
      total: tokenList.length,
      active: tokenList.filter(t => t.status === 'active').length,
      inactive: tokenList.filter(t => t.status === 'inactive').length,
      stablecoins: tokenList.filter(t => 
        t.symbol === 'USDT' || t.symbol === 'USDC' || t.symbol === 'DAI' || t.symbol === 'BUSD'
      ).length,
      verified: tokenList.filter(t => t.contractVerified).length,
      pending: tokenList.filter(t => t.status === 'pending').length,
    };
  }, []);

  // تحويل البيانات من API إلى تنسيق المكون
  const formatTokenData = useCallback((tokenData: DefaultTokenData): Token => {
    return {
      id: tokenData.id.toString(),
      name: tokenData.token_name,
      symbol: tokenData.token_symbol,
      address: tokenData.token_address,
      networkId: tokenData.network_id.toString(),
      type: tokenData.token_symbol === 'ETH' || tokenData.token_symbol === 'BNB' ? 'native' : 'erc20',
      decimals: tokenData.decimals,
      totalSupply: 0,
      marketCap: parseFloat(tokenData.market_cap?.toString() || '0') || 0,
      price: parseFloat(tokenData.current_price_usd?.toString() || '1') || 1.0,
      volume24h: parseFloat(tokenData.volume_24h?.toString() || '0') || 0,
      status: tokenData.is_active ? 'active' : 'inactive',
      isEnabled: tokenData.is_active,
      contractVerified: true,
      lastUpdated: tokenData.updated_at || tokenData.created_at || new Date().toISOString(),
      holders: tokenData.total_offers || 0,
      transfers24h: tokenData.total_trades || 0
    };
  }, []);

  // جلب العملات من قاعدة البيانات
  const fetchTokensFromDatabase = useCallback(async (): Promise<Token[]> => {
    try {
      console.log('🔄 Fetching tokens from database...');
      const response = await fetch('/api/enhanced-contracts/database-sync?action=tokens&active=1', {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        }
      });

      console.log('📡 Database response status:', response.status);
      console.log('📡 Database response headers:', response.headers.get('content-type'));

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Database API Error Response:', errorText);
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // التحقق من نوع المحتوى
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        const responseText = await response.text();
        console.error('❌ Non-JSON response received:', responseText.substring(0, 200));
        throw new Error('الاستجابة ليست JSON صالح');
      }

      const data = await response.json();

      if (data.success && data.data.tokens.length > 0) {
        const formattedTokens = data.data.tokens.map(formatTokenData);
        console.log('✅ Tokens loaded from database:', formattedTokens);
        return formattedTokens;
      }
      return [];
    } catch (error) {
      console.error('Error fetching tokens from database:', error);
      return [];
    }
  }, [formatTokenData]);

  // جلب العملات من البلوك تشين
  const fetchTokensFromBlockchain = useCallback(async (): Promise<Token[]> => {
    try {
      console.log('🔄 Fetching tokens from blockchain...');

      // إضافة timeout للطلب
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 ثانية timeout

      const response = await fetch('/api/enhanced-contracts/tokens?action=tokens&active=1', {
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        }
      });

      clearTimeout(timeoutId);

      console.log('📡 Blockchain tokens response status:', response.status);
      console.log('📡 Blockchain tokens response headers:', response.headers.get('content-type'));

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Blockchain Tokens API Error Response:', errorText);
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        const responseText = await response.text();
        console.error('❌ Non-JSON response received:', responseText.substring(0, 200));
        throw new Error('الاستجابة ليست JSON صالح');
      }

      const data = await response.json();

      if (data.success && data.data && data.data.tokens) {
        const formattedTokens = data.data.tokens.map(formatTokenData);
        console.log('✅ Tokens loaded from blockchain:', formattedTokens);
        return formattedTokens;
      }
      throw new Error(data.message || 'فشل في جلب العملات');
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        console.error('⏰ Blockchain tokens request timeout');
        throw new Error('انتهت مهلة الاتصال بالبلوك تشين');
      } else {
        console.error('Error fetching tokens from blockchain:', error);
        throw error;
      }
    }
  }, [formatTokenData]);

  // بيانات افتراضية للعملات
  const getDefaultTokens = useCallback((): Token[] => {
    return [
      {
        id: '1',
        name: 'Tether USD',
        symbol: 'USDT',
        address: '******************************************',
        networkId: '1',
        type: 'erc20',
        decimals: 6,
        totalSupply: 83000000000,
        marketCap: 83000000000,
        price: 1.00,
        volume24h: 50000000000,
        status: 'active',
        isEnabled: true,
        contractVerified: true,
        lastUpdated: new Date().toISOString(),
        holders: 4500000,
        transfers24h: 2000000
      },
      {
        id: '2',
        name: 'USD Coin',
        symbol: 'USDC',
        address: '******************************************',
        networkId: '1',
        type: 'erc20',
        decimals: 6,
        totalSupply: 25000000000,
        marketCap: 25000000000,
        price: 1.00,
        volume24h: 8000000000,
        status: 'active',
        isEnabled: true,
        contractVerified: true,
        lastUpdated: new Date().toISOString(),
        holders: 2000000,
        transfers24h: 800000
      }
    ];
  }, []);

  // حفظ العملات في قاعدة البيانات
  const saveTokensToDatabase = useCallback(async (tokensToSave: Token[]): Promise<void> => {
    try {
      console.log('💾 Saving tokens to database...');
      
      const tokensForAPI = tokensToSave.map(token => ({
        id: token.id,
        token_name: token.name,
        token_symbol: token.symbol,
        token_address: token.address,
        network_id: token.networkId,
        decimals: token.decimals,
        is_stablecoin: token.symbol === 'USDT' || token.symbol === 'USDC' || token.symbol === 'DAI',
        is_active: token.isEnabled,
        coingecko_id: null,
        min_trade_amount: 1,
        max_trade_amount: 1000000,
        daily_volume_limit: 10000000,
        platform_fee_rate: 0.001,
        market_cap: token.marketCap,
        current_price_usd: token.price,
        volume_24h: token.volume24h,
        total_offers: token.holders,
        total_trades: token.transfers24h
      }));

      const response = await fetch('/api/enhanced-contracts/database-sync?action=sync-tokens', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tokens: tokensForAPI
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('✅ Tokens saved to database:', result);
    } catch (error) {
      console.error('Error saving tokens to database:', error);
      throw error;
    }
  }, []);

  // جلب العملات (من قاعدة البيانات أولاً، ثم من البلوك تشين)
  const fetchTokens = useCallback(async (fromBlockchain = false) => {
    console.log('🔄 Starting fetchTokens, fromBlockchain:', fromBlockchain);

    setIsLoading(true);
    setError(null);

    try {
      let tokensData: Token[] = [];

      if (!fromBlockchain) {
        // محاولة جلب من قاعدة البيانات أولاً
        try {
          tokensData = await fetchTokensFromDatabase();
        } catch (dbError) {
          console.warn('⚠️ Database fetch failed, will try blockchain:', dbError);
        }
      }

      // إذا لم توجد بيانات في قاعدة البيانات أو طلب تحديث من البلوك تشين
      if (tokensData.length === 0 || fromBlockchain) {
        try {
          tokensData = await fetchTokensFromBlockchain();

          // حفظ البيانات في قاعدة البيانات (في الخلفية)
          if (tokensData.length > 0) {
            saveTokensToDatabase(tokensData).catch(syncError => {
              console.warn('⚠️ Failed to sync tokens to database:', syncError);
            });
          }
        } catch (blockchainError) {
          console.warn('⚠️ Blockchain fetch failed, using defaults:', blockchainError);
          tokensData = getDefaultTokens();
        }
      }

      // إذا لم نحصل على أي بيانات، استخدم البيانات الافتراضية
      if (tokensData.length === 0) {
        tokensData = getDefaultTokens();
      }

      setTokens(tokensData);
      console.log('✅ Tokens loaded successfully:', tokensData.length);
    } catch (error) {
      console.error('❌ Error fetching tokens:', error);
      setError('فشل في جلب بيانات العملات - سيتم استخدام البيانات الافتراضية');
      setTokens(getDefaultTokens());
    } finally {
      setIsLoading(false);
    }
  }, [fetchTokensFromDatabase, fetchTokensFromBlockchain, saveTokensToDatabase, getDefaultTokens]);

  // تحديث حالة عملة معينة
  const toggleToken = useCallback(async (tokenId: string) => {
    try {
      const token = tokens.find(t => t.id === tokenId);
      if (!token) return;

      const updatedToken: Token = {
        ...token,
        isEnabled: !token.isEnabled,
        status: (!token.isEnabled ? 'active' : 'inactive') as 'active' | 'inactive'
      };

      setTokens(prev => prev.map(t => t.id === tokenId ? updatedToken : t));
      console.log(`🔄 Token ${token.symbol} ${updatedToken.isEnabled ? 'enabled' : 'disabled'}`);
    } catch (error) {
      console.error('Error toggling token:', error);
      setError('فشل في تحديث حالة العملة');
    }
  }, [tokens]);

  return {
    tokens,
    pendingTokens,
    isLoading,
    error,
    setTokens,
    setPendingTokens,
    setError,
    fetchTokens,
    toggleToken,
    saveTokensToDatabase,
    getTokenStats,
    formatTokenData
  };
};
